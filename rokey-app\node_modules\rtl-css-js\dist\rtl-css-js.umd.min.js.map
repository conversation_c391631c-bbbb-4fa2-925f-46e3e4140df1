{"version": 3, "file": "rtl-css-js.umd.min.js", "sources": ["../src/internal/utils.js", "../src/internal/property-value-converters.js", "../src/internal/convert.js"], "sourcesContent": ["/**\n * Takes an array of [keyValue1, keyValue2] pairs and creates an object of {keyValue1: keyValue2, keyValue2: keyValue1}\n * @param {Array} array the array of pairs\n * @return {Object} the {key, value} pair object\n */\nfunction arrayToObject(array) {\n  return array.reduce((obj, [prop1, prop2]) => {\n    obj[prop1] = prop2\n    obj[prop2] = prop1\n    return obj\n  }, {})\n}\n\nfunction isBoolean(val) {\n  return typeof val === 'boolean'\n}\n\nfunction isFunction(val) {\n  return typeof val === 'function'\n}\n\nfunction isNumber(val) {\n  return typeof val === 'number'\n}\n\nfunction isNullOrUndefined(val) {\n  return val === null || typeof val === 'undefined'\n}\n\nfunction isObject(val) {\n  return val && typeof val === 'object'\n}\n\nfunction isString(val) {\n  return typeof val === 'string'\n}\n\nfunction includes(inclusive, inclusee) {\n  return inclusive.indexOf(inclusee) !== -1\n}\n\n/**\n * Flip the sign of a CSS value, possibly with a unit.\n *\n * We can't just negate the value with unary minus due to the units.\n *\n * @private\n * @param {String} value - the original value (for example 77%)\n * @return {String} the result (for example -77%)\n */\nfunction flipSign(value) {\n  if (parseFloat(value) === 0) {\n    // Don't mangle zeroes\n    return value\n  }\n\n  if (value[0] === '-') {\n    return value.slice(1)\n  }\n\n  return `-${value}`\n}\n\nfunction flipTransformSign(match, prefix, offset, suffix) {\n  return prefix + flipSign(offset) + suffix\n}\n\n/**\n * Takes a percentage for background position and inverts it.\n * This was copied and modified from CSSJanus:\n * https://github.com/cssjanus/cssjanus/blob/4245f834365f6cfb0239191a151432fb85abab23/src/cssjanus.js#L152-L175\n * @param {String} value - the original value (for example 77%)\n * @return {String} the result (for example 23%)\n */\nfunction calculateNewBackgroundPosition(value) {\n  const idx = value.indexOf('.')\n  if (idx === -1) {\n    value = `${100 - parseFloat(value)}%`\n  } else {\n    // Two off, one for the \"%\" at the end, one for the dot itself\n    const len = value.length - idx - 2\n    value = 100 - parseFloat(value)\n    value = `${value.toFixed(len)}%`\n  }\n  return value\n}\n\n/**\n * This takes a list of CSS values and converts it to an array\n * @param {String} value - something like `1px`, `1px 2em`, or `3pt rgb(150, 230, 550) 40px calc(100% - 5px)`\n * @return {Array} the split values (for example: `['3pt', 'rgb(150, 230, 550)', '40px', 'calc(100% - 5px)']`)\n */\nfunction getValuesAsList(value) {\n  return (\n    value\n      .replace(/ +/g, ' ') // remove all extraneous spaces\n      .split(' ')\n      .map(i => i.trim()) // get rid of extra space before/after each item\n      .filter(Boolean) // get rid of empty strings\n      // join items which are within parenthese\n      // luckily `calc (100% - 5px)` is invalid syntax and it must be `calc(100% - 5px)`, otherwise this would be even more complex\n      .reduce(\n        ({list, state}, item) => {\n          const openParansCount = (item.match(/\\(/g) || []).length\n          const closedParansCount = (item.match(/\\)/g) || []).length\n          if (state.parensDepth > 0) {\n            list[list.length - 1] = `${list[list.length - 1]} ${item}`\n          } else {\n            list.push(item)\n          }\n          state.parensDepth += openParansCount - closedParansCount\n          return {list, state}\n        },\n        {list: [], state: {parensDepth: 0}},\n      ).list\n  )\n}\n\n/**\n * This is intended for properties that are `top right bottom left` and will switch them to `top left bottom right`\n * @param {String} value - `1px 2px 3px 4px` for example, but also handles cases where there are too few/too many and\n * simply returns the value in those cases (which is the correct behavior)\n * @return {String} the result - `1px 4px 3px 2px` for example.\n */\nfunction handleQuartetValues(value) {\n  const splitValues = getValuesAsList(value)\n  if (splitValues.length <= 3 || splitValues.length > 4) {\n    return value\n  }\n  const [top, right, bottom, left] = splitValues\n  return [top, left, bottom, right].join(' ')\n}\n\n/**\n *\n * @param {String|Number|Object} value css property value to test\n * @returns If the css property value can(should?) have an RTL equivalent\n */\nfunction canConvertValue(value) {\n  return !isBoolean(value) && !isNullOrUndefined(value)\n}\n\n/**\n * Splits a shadow style into its separate shadows using the comma delimiter, but creating an exception\n * for comma separated values in parentheses often used for rgba colours.\n * @param {String} value\n * @returns {Array} array of all box shadow values in the string\n */\nfunction splitShadow(value) {\n  const shadows = []\n  let start = 0\n  let end = 0\n  let rgba = false\n  while (end < value.length) {\n    if (!rgba && value[end] === ',') {\n      shadows.push(value.substring(start, end).trim())\n      end++\n      start = end\n    } else if (value[end] === `(`) {\n      rgba = true\n      end++\n    } else if (value[end] === ')') {\n      rgba = false\n      end++\n    } else {\n      end++\n    }\n  }\n\n  // push the last shadow value if there is one\n  // istanbul ignore next\n  if (start != end) {\n    shadows.push(value.substring(start, end + 1))\n  }\n\n  return shadows\n}\n\nexport {\n  arrayToObject,\n  calculateNewBackgroundPosition,\n  canConvertValue,\n  flipTransformSign as calculateNewTranslate,\n  flipTransformSign,\n  flipSign,\n  handleQuartetValues,\n  includes,\n  isBoolean,\n  isFunction,\n  isNumber,\n  isNullOrUndefined,\n  isObject,\n  isString,\n  getValuesAsList,\n  splitShadow,\n}\n", "import {\n  includes,\n  isNumber,\n  calculateNewBackgroundPosition,\n  flipTransformSign,\n  handleQuartetValues,\n  getValuesAsList,\n  splitShadow,\n} from './utils'\n\n// some values require a little fudging, that fudging goes here.\nconst propertyValueConverters = {\n  padding({value}) {\n    if (isNumber(value)) {\n      return value\n    }\n    return handleQuartetValues(value)\n  },\n  textShadow({value}) {\n    const flippedShadows = splitShadow(value).map(shadow => {\n      // intentionally leaving off the `g` flag here because we only want to change the first number (which is the offset-x)\n      return shadow.replace(\n        /(^|\\s)(-*)([.|\\d]+)/,\n        (match, whiteSpace, negative, number) => {\n          if (number === '0') {\n            return match\n          }\n          const doubleNegative = negative === '' ? '-' : ''\n          return `${whiteSpace}${doubleNegative}${number}`\n        },\n      )\n    })\n\n    return flippedShadows.join(',')\n  },\n  borderColor({value}) {\n    return handleQuartetValues(value)\n  },\n  borderRadius({value}) {\n    if (isNumber(value)) {\n      return value\n    }\n    if (includes(value, '/')) {\n      const [radius1, radius2] = value.split('/')\n      const convertedRadius1 = propertyValueConverters.borderRadius({\n        value: radius1.trim(),\n      })\n      const convertedRadius2 = propertyValueConverters.borderRadius({\n        value: radius2.trim(),\n      })\n      return `${convertedRadius1} / ${convertedRadius2}`\n    }\n    const splitValues = getValuesAsList(value)\n    switch (splitValues.length) {\n      case 2: {\n        return splitValues.reverse().join(' ')\n      }\n      case 4: {\n        const [topLeft, topRight, bottomRight, bottomLeft] = splitValues\n        return [topRight, topLeft, bottomLeft, bottomRight].join(' ')\n      }\n      default: {\n        return value\n      }\n    }\n  },\n  background({\n    value,\n    valuesToConvert,\n    isRtl,\n    bgImgDirectionRegex,\n    bgPosDirectionRegex,\n  }) {\n    if (isNumber(value)) {\n      return value\n    }\n\n    // Yeah, this is in need of a refactor 🙃...\n    // but this property is a tough cookie 🍪\n    // get the backgroundPosition out of the string by removing everything that couldn't be the backgroundPosition value\n    const backgroundPositionValue = value\n      .replace(\n        /(url\\(.*?\\))|(rgba?\\(.*?\\))|(hsl\\(.*?\\))|(#[a-fA-F0-9]+)|((^| )(\\D)+( |$))/g,\n        '',\n      )\n      .trim()\n    // replace that backgroundPosition value with the converted version\n    value = value.replace(\n      backgroundPositionValue,\n      propertyValueConverters.backgroundPosition({\n        value: backgroundPositionValue,\n        valuesToConvert,\n        isRtl,\n        bgPosDirectionRegex,\n      }),\n    )\n    // do the backgroundImage value replacing on the whole value (because why not?)\n    return propertyValueConverters.backgroundImage({\n      value,\n      valuesToConvert,\n      bgImgDirectionRegex,\n    })\n  },\n  backgroundImage({value, valuesToConvert, bgImgDirectionRegex}) {\n    if (!includes(value, 'url(') && !includes(value, 'linear-gradient(')) {\n      return value\n    }\n    return value.replace(bgImgDirectionRegex, (match, g1, group2) => {\n      return match.replace(group2, valuesToConvert[group2])\n    })\n  },\n  backgroundPosition({value, valuesToConvert, isRtl, bgPosDirectionRegex}) {\n    return (\n      value\n        // intentionally only grabbing the first instance of this because that represents `left`\n        .replace(isRtl ? /^((-|\\d|\\.)+%)/ : null, (match, group) =>\n          calculateNewBackgroundPosition(group),\n        )\n        .replace(bgPosDirectionRegex, match => valuesToConvert[match])\n    )\n  },\n  backgroundPositionX({value, valuesToConvert, isRtl, bgPosDirectionRegex}) {\n    if (isNumber(value)) {\n      return value\n    }\n    return propertyValueConverters.backgroundPosition({\n      value,\n      valuesToConvert,\n      isRtl,\n      bgPosDirectionRegex,\n    })\n  },\n  transition({value, propertiesToConvert}) {\n    return value\n      .split(/,\\s*/g)\n      .map(transition => {\n        const values = transition.split(' ')\n\n        // Property is always defined first\n        values[0] = propertiesToConvert[values[0]] || values[0]\n\n        return values.join(' ')\n      })\n      .join(', ')\n  },\n  transitionProperty({value, propertiesToConvert}) {\n    return value\n      .split(/,\\s*/g)\n      .map(prop => propertiesToConvert[prop] || prop)\n      .join(', ')\n  },\n  transform({value}) {\n    // This was copied and modified from CSSJanus:\n    // https://github.com/cssjanus/cssjanus/blob/4a40f001b1ba35567112d8b8e1d9d95eda4234c3/src/cssjanus.js#L152-L153\n    const nonAsciiPattern = '[^\\\\u0020-\\\\u007e]'\n    const unicodePattern = '(?:(?:\\\\[0-9a-f]{1,6})(?:\\\\r\\\\n|\\\\s)?)'\n    const numPattern = '(?:[0-9]*\\\\.[0-9]+|[0-9]+)'\n    const unitPattern = '(?:em|ex|px|cm|mm|in|pt|pc|deg|rad|grad|ms|s|hz|khz|%)'\n    const escapePattern = `(?:${unicodePattern}|\\\\\\\\[^\\\\r\\\\n\\\\f0-9a-f])`\n    const nmstartPattern = `(?:[_a-z]|${nonAsciiPattern}|${escapePattern})`\n    const nmcharPattern = `(?:[_a-z0-9-]|${nonAsciiPattern}|${escapePattern})`\n    const identPattern = `-?${nmstartPattern}${nmcharPattern}*`\n    const quantPattern = `${numPattern}(?:\\\\s*${unitPattern}|${identPattern})?`\n    const signedQuantPattern = `((?:-?${quantPattern})|(?:inherit|auto))`\n    const translateXRegExp = new RegExp(\n      `(translateX\\\\s*\\\\(\\\\s*)${signedQuantPattern}(\\\\s*\\\\))`,\n      'gi',\n    )\n    const translateRegExp = new RegExp(\n      `(translate\\\\s*\\\\(\\\\s*)${signedQuantPattern}((?:\\\\s*,\\\\s*${signedQuantPattern}){0,1}\\\\s*\\\\))`,\n      'gi',\n    )\n    const translate3dRegExp = new RegExp(\n      `(translate3d\\\\s*\\\\(\\\\s*)${signedQuantPattern}((?:\\\\s*,\\\\s*${signedQuantPattern}){0,2}\\\\s*\\\\))`,\n      'gi',\n    )\n    const rotateRegExp = new RegExp(\n      `(rotate[ZY]?\\\\s*\\\\(\\\\s*)${signedQuantPattern}(\\\\s*\\\\))`,\n      'gi',\n    )\n    return value\n      .replace(translateXRegExp, flipTransformSign)\n      .replace(translateRegExp, flipTransformSign)\n      .replace(translate3dRegExp, flipTransformSign)\n      .replace(rotateRegExp, flipTransformSign)\n  },\n}\n\npropertyValueConverters.objectPosition =\n  propertyValueConverters.backgroundPosition\npropertyValueConverters.margin = propertyValueConverters.padding\npropertyValueConverters.borderWidth = propertyValueConverters.padding\npropertyValueConverters.boxShadow = propertyValueConverters.textShadow\npropertyValueConverters.webkitBoxShadow = propertyValueConverters.boxShadow\npropertyValueConverters.mozBoxShadow = propertyValueConverters.boxShadow\npropertyValueConverters.WebkitBoxShadow = propertyValueConverters.boxShadow\npropertyValueConverters.MozBoxShadow = propertyValueConverters.boxShadow\npropertyValueConverters.borderStyle = propertyValueConverters.borderColor\npropertyValueConverters.webkitTransform = propertyValueConverters.transform\npropertyValueConverters.mozTransform = propertyValueConverters.transform\npropertyValueConverters.WebkitTransform = propertyValueConverters.transform\npropertyValueConverters.MozTransform = propertyValueConverters.transform\npropertyValueConverters.transformOrigin =\n  propertyValueConverters.backgroundPosition\npropertyValueConverters.webkitTransformOrigin =\n  propertyValueConverters.transformOrigin\npropertyValueConverters.mozTransformOrigin =\n  propertyValueConverters.transformOrigin\npropertyValueConverters.WebkitTransformOrigin =\n  propertyValueConverters.transformOrigin\npropertyValueConverters.MozTransformOrigin =\n  propertyValueConverters.transformOrigin\npropertyValueConverters.webkitTransition = propertyValueConverters.transition\npropertyValueConverters.mozTransition = propertyValueConverters.transition\npropertyValueConverters.WebkitTransition = propertyValueConverters.transition\npropertyValueConverters.MozTransition = propertyValueConverters.transition\npropertyValueConverters.webkitTransitionProperty =\n  propertyValueConverters.transitionProperty\npropertyValueConverters.mozTransitionProperty =\n  propertyValueConverters.transitionProperty\npropertyValueConverters.WebkitTransitionProperty =\n  propertyValueConverters.transitionProperty\npropertyValueConverters.MozTransitionProperty =\n  propertyValueConverters.transitionProperty\n\n// kebab-case versions\n\npropertyValueConverters['text-shadow'] = propertyValueConverters.textShadow\npropertyValueConverters['border-color'] = propertyValueConverters.borderColor\npropertyValueConverters['border-radius'] = propertyValueConverters.borderRadius\npropertyValueConverters['background-image'] =\n  propertyValueConverters.backgroundImage\npropertyValueConverters['background-position'] =\n  propertyValueConverters.backgroundPosition\npropertyValueConverters['background-position-x'] =\n  propertyValueConverters.backgroundPositionX\npropertyValueConverters['object-position'] =\n  propertyValueConverters.objectPosition\npropertyValueConverters['border-width'] = propertyValueConverters.padding\npropertyValueConverters['box-shadow'] = propertyValueConverters.textShadow\npropertyValueConverters['-webkit-box-shadow'] =\n  propertyValueConverters.textShadow\npropertyValueConverters['-moz-box-shadow'] = propertyValueConverters.textShadow\npropertyValueConverters['border-style'] = propertyValueConverters.borderColor\npropertyValueConverters['-webkit-transform'] = propertyValueConverters.transform\npropertyValueConverters['-moz-transform'] = propertyValueConverters.transform\npropertyValueConverters['transform-origin'] =\n  propertyValueConverters.transformOrigin\npropertyValueConverters['-webkit-transform-origin'] =\n  propertyValueConverters.transformOrigin\npropertyValueConverters['-moz-transform-origin'] =\n  propertyValueConverters.transformOrigin\npropertyValueConverters['-webkit-transition'] =\n  propertyValueConverters.transition\npropertyValueConverters['-moz-transition'] = propertyValueConverters.transition\npropertyValueConverters['transition-property'] =\n  propertyValueConverters.transitionProperty\npropertyValueConverters['-webkit-transition-property'] =\n  propertyValueConverters.transitionProperty\npropertyValueConverters['-moz-transition-property'] =\n  propertyValueConverters.transitionProperty\n\nexport default propertyValueConverters\n", "import {\n  includes,\n  arrayToObject,\n  isFunction,\n  isNumber,\n  isObject,\n  isString,\n  canConvertValue,\n} from './utils'\nimport propertyValueConverters from './property-value-converters'\n\n// this will be an object of properties that map to their corresponding rtl property (their doppelganger)\nexport const propertiesToConvert = arrayToObject([\n  ['paddingLeft', 'paddingRight'],\n  ['marginLeft', 'marginRight'],\n  ['left', 'right'],\n  ['borderLeft', 'borderRight'],\n  ['borderLeftColor', 'borderRightColor'],\n  ['borderLeftStyle', 'borderRightStyle'],\n  ['borderLeftWidth', 'borderRightWidth'],\n  ['borderTopLeftRadius', 'borderTopRightRadius'],\n  ['borderBottomLeftRadius', 'borderBottomRightRadius'],\n  // kebab-case versions\n  ['padding-left', 'padding-right'],\n  ['margin-left', 'margin-right'],\n  ['border-left', 'border-right'],\n  ['border-left-color', 'border-right-color'],\n  ['border-left-style', 'border-right-style'],\n  ['border-left-width', 'border-right-width'],\n  ['border-top-left-radius', 'border-top-right-radius'],\n  ['border-bottom-left-radius', 'border-bottom-right-radius'],\n])\n\nexport const propsToIgnore = ['content']\n\n// this is the same as the propertiesToConvert except for values\nexport const valuesToConvert = arrayToObject([\n  ['ltr', 'rtl'],\n  ['left', 'right'],\n  ['w-resize', 'e-resize'],\n  ['sw-resize', 'se-resize'],\n  ['nw-resize', 'ne-resize'],\n])\n\n// Sorry for the regex 😞, but basically thisis used to replace _every_ instance of\n// `ltr`, `rtl`, `right`, and `left` in `backgroundimage` with the corresponding opposite.\n// A situation we're accepting here:\n// url('/left/right/rtl/ltr.png') will be changed to url('/right/left/ltr/rtl.png')\n// Definite trade-offs here, but I think it's a good call.\nconst bgImgDirectionRegex = new RegExp(\n  '(^|\\\\W|_)((ltr)|(rtl)|(left)|(right))(\\\\W|_|$)',\n  'g',\n)\nconst bgPosDirectionRegex = new RegExp('(left)|(right)')\n\n/**\n * converts properties and values in the CSS in JS object to their corresponding RTL values\n * @param {Object} object the CSS in JS object\n * @return {Object} the RTL converted object\n */\nexport function convert(object) {\n  return Object.keys(object).reduce(\n    (newObj, originalKey) => {\n      let originalValue = object[originalKey]\n      if (isString(originalValue)) {\n        // you're welcome to later code 😺\n        originalValue = originalValue.trim()\n      }\n\n      // Some properties should never be transformed\n      if (includes(propsToIgnore, originalKey)) {\n        newObj[originalKey] = originalValue\n        return newObj\n      }\n\n      const {key, value} = convertProperty(originalKey, originalValue)\n      newObj[key] = value\n      return newObj\n    },\n    Array.isArray(object) ? [] : {},\n  )\n}\n\n/**\n * Converts a property and its value to the corresponding RTL key and value\n * @param {String} originalKey the original property key\n * @param {Number|String|Object} originalValue the original css property value\n * @return {Object} the new {key, value} pair\n */\nexport function convertProperty(originalKey, originalValue) {\n  const isNoFlip = /\\/\\*\\s?@noflip\\s?\\*\\//.test(originalValue)\n  const key = isNoFlip ? originalKey : getPropertyDoppelganger(originalKey)\n  const value = isNoFlip\n    ? originalValue\n    : getValueDoppelganger(key, originalValue)\n  return {key, value}\n}\n\n/**\n * This gets the RTL version of the given property if it has a corresponding RTL property\n * @param {String} property the name of the property\n * @return {String} the name of the RTL property\n */\nexport function getPropertyDoppelganger(property) {\n  return propertiesToConvert[property] || property\n}\n\n/**\n * This converts the given value to the RTL version of that value based on the key\n * @param {String} key this is the key (note: this should be the RTL version of the originalKey)\n * @param {String|Number|Object} originalValue the original css property value. If it's an object, then we'll convert that as well\n * @return {String|Number|Object} the converted value\n */\nexport function getValueDoppelganger(key, originalValue) {\n  if (!canConvertValue(originalValue)) {\n    return originalValue\n  }\n\n  if (isObject(originalValue)) {\n    return convert(originalValue) // recursion 🌀\n  }\n  const isNum = isNumber(originalValue)\n  const isFunc = isFunction(originalValue)\n\n  const importantlessValue =\n    isNum || isFunc\n      ? originalValue\n      : originalValue.replace(/ !important.*?$/, '')\n  const isImportant =\n    !isNum && importantlessValue.length !== originalValue.length\n  const valueConverter = propertyValueConverters[key]\n  let newValue\n  if (valueConverter) {\n    newValue = valueConverter({\n      value: importantlessValue,\n      valuesToConvert,\n      propertiesToConvert,\n      isRtl: true,\n      bgImgDirectionRegex,\n      bgPosDirectionRegex,\n    })\n  } else {\n    newValue = valuesToConvert[importantlessValue] || importantlessValue\n  }\n  if (isImportant) {\n    return `${newValue} !important`\n  }\n  return newValue\n}\n"], "names": ["arrayToObject", "array", "reduce", "obj", "prop1", "prop2", "isNumber", "val", "includes", "inclusive", "inclusee", "indexOf", "flipTransformSign", "match", "prefix", "offset", "suffix", "value", "parseFloat", "slice", "getValuesAsList", "replace", "split", "map", "i", "trim", "filter", "Boolean", "item", "list", "state", "openParansCount", "length", "closedParansCount", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "handleQuartetValues", "splitValues", "top", "right", "bottom", "join", "propertyValueConverters", "padding", "textShadow", "shadows", "start", "end", "rgba", "substring", "splitShadow", "shadow", "whiteSpace", "negative", "number", "borderColor", "borderRadius", "radius1", "radius2", "reverse", "topLeft", "topRight", "bottomRight", "background", "valuesToConvert", "isRtl", "bgImgDirectionRegex", "bgPosDirectionRegex", "backgroundPositionValue", "backgroundPosition", "backgroundImage", "g1", "group2", "group", "idx", "len", "toFixed", "calculateNewBackgroundPosition", "backgroundPositionX", "transition", "propertiesToConvert", "values", "transitionProperty", "prop", "transform", "escapePattern", "signedQuantPattern", "translateXRegExp", "RegExp", "translateRegExp", "translate3dRegExp", "rotateRegExp", "objectPosition", "margin", "borderWidth", "boxShadow", "webkitBoxShadow", "mozBoxShadow", "WebkitBoxShadow", "MozBoxShadow", "borderStyle", "webkitTransform", "mozTransform", "WebkitTransform", "MozTransform", "transform<PERSON><PERSON>in", "webkitTransformOrigin", "mozTransformOrigin", "WebkitTransformOrigin", "MozTransformOrigin", "webkitTransition", "mozTransition", "WebkitTransition", "MozTransition", "webkitTransitionProperty", "mozTransitionProperty", "WebkitTransitionProperty", "MozTransitionProperty", "propsToIgnore", "convert", "object", "Object", "keys", "newObj", "original<PERSON>ey", "originalValue", "isNoFlip", "test", "key", "property", "isNullOrUndefined", "canConvertValue", "isObject", "newValue", "isNum", "isFunc", "isFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isImportant", "valueConverter", "getValueDoppelganger", "convertProperty", "Array", "isArray"], "mappings": "+LAKA,SAASA,EAAcC,GACrB,OAAOA,EAAMC,QAAO,SAACC,KAAwB,IAAlBC,OAAOC,OAGhC,OAFAF,EAAIC,GAASC,EACbF,EAAIE,GAASD,EACND,IACN,IAWL,SAASG,EAASC,GAChB,MAAsB,iBAARA,EAehB,SAASC,EAASC,EAAWC,GAC3B,OAAwC,IAAjCD,EAAUE,QAAQD,GAyB3B,SAASE,EAAkBC,EAAOC,EAAQC,EAAQC,GAChD,OAAOF,GAdSG,EAcSF,EAbC,IAAtBG,WAAWD,GAENA,EAGQ,MAAbA,EAAM,GACDA,EAAME,MAAM,OAGVF,GAIwBD,EAdrC,IAAkBC,EA0ClB,SAASG,EAAgBH,GACvB,OACEA,EACGI,QAAQ,MAAO,KACfC,MAAM,KACNC,KAAI,SAAAC,GAAC,OAAIA,EAAEC,UACXC,OAAOC,SAGPzB,QACC,WAAgB0B,GAAS,IAAvBC,IAAAA,KAAMC,IAAAA,MACAC,GAAmBH,EAAKf,MAAM,QAAU,IAAImB,OAC5CC,GAAqBL,EAAKf,MAAM,QAAU,IAAImB,OAOpD,OANIF,EAAMI,YAAc,EACtBL,EAAKA,EAAKG,OAAS,GAAQH,EAAKA,EAAKG,OAAS,OAAMJ,EAEpDC,EAAKM,KAAKP,GAEZE,EAAMI,aAAeH,EAAkBE,EAChC,CAACJ,KAAAA,EAAMC,MAAAA,KAEhB,CAACD,KAAM,GAAIC,MAAO,CAACI,YAAa,KAChCL,KAUR,SAASO,EAAoBnB,GAC3B,IAAMoB,EAAcjB,EAAgBH,GACpC,GAAIoB,EAAYL,QAAU,GAAKK,EAAYL,OAAS,EAClD,OAAOf,EAET,IAAOqB,EAA4BD,KAAvBE,EAAuBF,KAAhBG,EAAgBH,KACnC,MAAO,CAACC,EAD2BD,KAChBG,EAAQD,GAAOE,KAAK,KCvHzC,IAAMC,EAA0B,CAC9BC,oBAAiB,IAAR1B,IAAAA,MACP,OAAIX,EAASW,GACJA,EAEFmB,EAAoBnB,IAE7B2B,uBAeE,ODmHJ,SAAqB3B,GAKnB,IAJA,IAAM4B,EAAU,GACZC,EAAQ,EACRC,EAAM,EACNC,GAAO,EACJD,EAAM9B,EAAMe,QACZgB,GAAuB,MAAf/B,EAAM8B,SAIR9B,EAAM8B,IACfC,GAAO,EACPD,KACwB,MAAf9B,EAAM8B,IACfC,GAAO,EACPD,KAEAA,KAVAF,EAAQV,KAAKlB,EAAMgC,UAAUH,EAAOC,GAAKtB,QAEzCqB,IADAC,GAmBJ,OAJID,GAASC,GACXF,EAAQV,KAAKlB,EAAMgC,UAAUH,EAAOC,EAAM,IAGrCF,EC5JkBK,GADbjC,OACgCM,KAAI,SAAA4B,GAE5C,OAAOA,EAAO9B,QACZ,uBACA,SAACR,EAAOuC,EAAYC,EAAUC,GAC5B,MAAe,MAAXA,EACKzC,KAGCuC,GAD0B,KAAbC,EAAkB,IAAM,IACPC,QAKxBb,KAAK,MAE7Bc,wBACE,OAAOnB,IADInB,QAGbuC,yBAAsB,IAARvC,IAAAA,MACZ,GAAIX,EAASW,GACX,OAAOA,EAET,GAAIT,EAASS,EAAO,KAAM,CACxB,MAA2BA,EAAMK,MAAM,KAAhCmC,OAASC,OAOhB,OANyBhB,EAAwBc,aAAa,CAC5DvC,MAAOwC,EAAQhC,eAEQiB,EAAwBc,aAAa,CAC5DvC,MAAOyC,EAAQjC,SAInB,IAAMY,EAAcjB,EAAgBH,GACpC,OAAQoB,EAAYL,QAClB,KAAK,EACH,OAAOK,EAAYsB,UAAUlB,KAAK,KAEpC,KAAK,EACH,IAAOmB,EAA8CvB,KAArCwB,EAAqCxB,KAA3ByB,EAA2BzB,KACrD,MAAO,CAACwB,EAAUD,EADmCvB,KACdyB,GAAarB,KAAK,KAE3D,QACE,OAAOxB,IAIb8C,uBAMG,IALD9C,IAAAA,MACA+C,IAAAA,gBACAC,IAAAA,MACAC,IAAAA,oBACAC,IAAAA,oBAEA,GAAI7D,EAASW,GACX,OAAOA,EAMT,IAAMmD,EAA0BnD,EAC7BI,QACC,8EACA,IAEDI,OAYH,OAVAR,EAAQA,EAAMI,QACZ+C,EACA1B,EAAwB2B,mBAAmB,CACzCpD,MAAOmD,EACPJ,gBAAAA,EACAC,MAAAA,EACAE,oBAAAA,KAIGzB,EAAwB4B,gBAAgB,CAC7CrD,MAAAA,EACA+C,gBAAAA,EACAE,oBAAAA,KAGJI,4BAA+D,IAA9CrD,IAAAA,MAAO+C,IAAAA,gBAAiBE,IAAAA,oBACvC,OAAK1D,EAASS,EAAO,SAAYT,EAASS,EAAO,oBAG1CA,EAAMI,QAAQ6C,GAAqB,SAACrD,EAAO0D,EAAIC,GACpD,OAAO3D,EAAMQ,QAAQmD,EAAQR,EAAgBQ,OAHtCvD,GAMXoD,+BAAyE,IAArDpD,IAAAA,MAAO+C,IAAAA,gBAAiBC,IAAAA,MAAOE,IAAAA,oBACjD,OACElD,EAEGI,QAAQ4C,EAAQ,iBAAmB,MAAM,SAACpD,EAAO4D,GAAK,ODzC/D,SAAwCxD,GACtC,IAAMyD,EAAMzD,EAAMN,QAAQ,KAC1B,IAAa,IAAT+D,EACFzD,EAAW,IAAMC,WAAWD,WACvB,CAEL,IAAM0D,EAAM1D,EAAMe,OAAS0C,EAAM,EAEjCzD,GADAA,EAAQ,IAAMC,WAAWD,IACR2D,QAAQD,OAE3B,OAAO1D,ECgCC4D,CAA+BJ,MAEhCpD,QAAQ8C,GAAqB,SAAAtD,GAAK,OAAImD,EAAgBnD,OAG7DiE,gCAA0E,IAArD7D,IAAAA,MAAO+C,IAAAA,gBAAiBC,IAAAA,MAAOE,IAAAA,oBAClD,OAAI7D,EAASW,GACJA,EAEFyB,EAAwB2B,mBAAmB,CAChDpD,MAAAA,EACA+C,gBAAAA,EACAC,MAAAA,EACAE,oBAAAA,KAGJY,uBAAyC,IAA7B9D,IAAAA,MAAO+D,IAAAA,oBACjB,OAAO/D,EACJK,MAAM,SACNC,KAAI,SAAAwD,GACH,IAAME,EAASF,EAAWzD,MAAM,KAKhC,OAFA2D,EAAO,GAAKD,EAAoBC,EAAO,KAAOA,EAAO,GAE9CA,EAAOxC,KAAK,QAEpBA,KAAK,OAEVyC,+BAAiD,IAA7BjE,IAAAA,MAAO+D,IAAAA,oBACzB,OAAO/D,EACJK,MAAM,SACNC,KAAI,SAAA4D,GAAI,OAAIH,EAAoBG,IAASA,KACzC1C,KAAK,OAEV2C,sBAAmB,IAARnE,IAAAA,MAOHoE,sEAKAC,kIAJiDD,uCACGA,4BAIpDE,EAAmB,IAAIC,iCACDF,cAC1B,MAEIG,EAAkB,IAAID,gCACDF,kBAAkCA,mBAC3D,MAEII,EAAoB,IAAIF,kCACDF,kBAAkCA,mBAC7D,MAEIK,EAAe,IAAIH,kCACIF,cAC3B,MAEF,OAAOrE,EACJI,QAAQkE,EAAkB3E,GAC1BS,QAAQoE,EAAiB7E,GACzBS,QAAQqE,EAAmB9E,GAC3BS,QAAQsE,EAAc/E,KAI7B8B,EAAwBkD,eACtBlD,EAAwB2B,mBAC1B3B,EAAwBmD,OAASnD,EAAwBC,QACzDD,EAAwBoD,YAAcpD,EAAwBC,QAC9DD,EAAwBqD,UAAYrD,EAAwBE,WAC5DF,EAAwBsD,gBAAkBtD,EAAwBqD,UAClErD,EAAwBuD,aAAevD,EAAwBqD,UAC/DrD,EAAwBwD,gBAAkBxD,EAAwBqD,UAClErD,EAAwByD,aAAezD,EAAwBqD,UAC/DrD,EAAwB0D,YAAc1D,EAAwBa,YAC9Db,EAAwB2D,gBAAkB3D,EAAwB0C,UAClE1C,EAAwB4D,aAAe5D,EAAwB0C,UAC/D1C,EAAwB6D,gBAAkB7D,EAAwB0C,UAClE1C,EAAwB8D,aAAe9D,EAAwB0C,UAC/D1C,EAAwB+D,gBACtB/D,EAAwB2B,mBAC1B3B,EAAwBgE,sBACtBhE,EAAwB+D,gBAC1B/D,EAAwBiE,mBACtBjE,EAAwB+D,gBAC1B/D,EAAwBkE,sBACtBlE,EAAwB+D,gBAC1B/D,EAAwBmE,mBACtBnE,EAAwB+D,gBAC1B/D,EAAwBoE,iBAAmBpE,EAAwBqC,WACnErC,EAAwBqE,cAAgBrE,EAAwBqC,WAChErC,EAAwBsE,iBAAmBtE,EAAwBqC,WACnErC,EAAwBuE,cAAgBvE,EAAwBqC,WAChErC,EAAwBwE,yBACtBxE,EAAwBwC,mBAC1BxC,EAAwByE,sBACtBzE,EAAwBwC,mBAC1BxC,EAAwB0E,yBACtB1E,EAAwBwC,mBAC1BxC,EAAwB2E,sBACtB3E,EAAwBwC,mBAI1BxC,EAAwB,eAAiBA,EAAwBE,WACjEF,EAAwB,gBAAkBA,EAAwBa,YAClEb,EAAwB,iBAAmBA,EAAwBc,aACnEd,EAAwB,oBACtBA,EAAwB4B,gBAC1B5B,EAAwB,uBACtBA,EAAwB2B,mBAC1B3B,EAAwB,yBACtBA,EAAwBoC,oBAC1BpC,EAAwB,mBACtBA,EAAwBkD,eAC1BlD,EAAwB,gBAAkBA,EAAwBC,QAClED,EAAwB,cAAgBA,EAAwBE,WAChEF,EAAwB,sBACtBA,EAAwBE,WAC1BF,EAAwB,mBAAqBA,EAAwBE,WACrEF,EAAwB,gBAAkBA,EAAwBa,YAClEb,EAAwB,qBAAuBA,EAAwB0C,UACvE1C,EAAwB,kBAAoBA,EAAwB0C,UACpE1C,EAAwB,oBACtBA,EAAwB+D,gBAC1B/D,EAAwB,4BACtBA,EAAwB+D,gBAC1B/D,EAAwB,yBACtBA,EAAwB+D,gBAC1B/D,EAAwB,sBACtBA,EAAwBqC,WAC1BrC,EAAwB,mBAAqBA,EAAwBqC,WACrErC,EAAwB,uBACtBA,EAAwBwC,mBAC1BxC,EAAwB,+BACtBA,EAAwBwC,mBAC1BxC,EAAwB,4BACtBA,EAAwBwC,mBCxPnB,IAAMF,EAAsBhF,EAAc,CAC/C,CAAC,cAAe,gBAChB,CAAC,aAAc,eACf,CAAC,OAAQ,SACT,CAAC,aAAc,eACf,CAAC,kBAAmB,oBACpB,CAAC,kBAAmB,oBACpB,CAAC,kBAAmB,oBACpB,CAAC,sBAAuB,wBACxB,CAAC,yBAA0B,2BAE3B,CAAC,eAAgB,iBACjB,CAAC,cAAe,gBAChB,CAAC,cAAe,gBAChB,CAAC,oBAAqB,sBACtB,CAAC,oBAAqB,sBACtB,CAAC,oBAAqB,sBACtB,CAAC,yBAA0B,2BAC3B,CAAC,4BAA6B,gCAGnBsH,EAAgB,CAAC,WAGjBtD,EAAkBhE,EAAc,CAC3C,CAAC,MAAO,OACR,CAAC,OAAQ,SACT,CAAC,WAAY,YACb,CAAC,YAAa,aACd,CAAC,YAAa,eAQVkE,EAAsB,IAAIsB,OAC9B,iDACA,KAEIrB,EAAsB,IAAIqB,OAAO,kBAOhC,SAAS+B,EAAQC,GACtB,OAAOC,OAAOC,KAAKF,GAAQtH,QACzB,SAACyH,EAAQC,GACP,IAAIC,EAAgBL,EAAOI,GAO3B,GFpCkB,iBE8BLC,IAEXA,EAAgBA,EAAcpG,QAI5BjB,EAAS8G,EAAeM,GAE1B,OADAD,EAAOC,GAAeC,EACfF,EAGT,MAcC,SAAyBC,EAAaC,GAC3C,IAAMC,EAAW,wBAAwBC,KAAKF,GACxCG,EAAMF,EAAWF,GAYeK,EAZuBL,EAatD5C,EAAoBiD,IAAaA,GAZlChH,EAAQ6G,EACVD,EAoBC,SAA8BG,EAAKH,GACxC,IFwBF,SAAyB5G,GACvB,QA9HiBV,EA8HCU,EA7HI,kBAARV,GAWhB,SAA2BA,GACzB,OAAOA,MAAAA,EAiHsB2H,CAAkBjH,IA9HjD,IAAmBV,EEqGZ4H,CAAgBN,GACnB,OAAOA,EAGT,GFzFF,SAAkBtH,GAChB,OAAOA,GAAsB,iBAARA,EEwFjB6H,CAASP,GACX,OAAON,EAAQM,GAEjB,IAUIQ,EAVEC,EAAQhI,EAASuH,GACjBU,EFzGR,SAAoBhI,GAClB,MAAsB,mBAARA,EEwGCiI,CAAWX,GAEpBY,EACJH,GAASC,EACLV,EACAA,EAAcxG,QAAQ,kBAAmB,IACzCqH,GACHJ,GAASG,EAAmBzG,SAAW6F,EAAc7F,OAClD2G,EAAiBjG,EAAwBsF,GAG7CK,EADEM,EACSA,EAAe,CACxB1H,MAAOwH,EACPzE,gBAAAA,EACAgB,oBAAAA,EACAf,OAAO,EACPC,oBAAAA,EACAC,oBAAAA,IAGSH,EAAgByE,IAAuBA,EAEpD,GAAIC,EACF,OAAUL,gBAEZ,OAAOA,EArDHO,CAAqBZ,EAAKH,GASzB,IAAiCI,EARtC,MAAO,CAACD,IAAAA,EAAK/G,MAAAA,GApBY4H,CAAgBjB,EAAaC,GAA3CG,IAAAA,IAAK/G,IAAAA,MAEZ,OADA0G,EAAOK,GAAO/G,EACP0G,IAETmB,MAAMC,QAAQvB,GAAU,GAAK"}