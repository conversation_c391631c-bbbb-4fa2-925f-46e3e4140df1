'use client';

import { BoltIcon } from '@heroicons/react/24/outline';
import { NodeProps, Handle, Position } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode } from '@/types/manualBuild';

export default function SwitchNode({ data }: NodeProps<WorkflowNode['data']>) {
  const config = data.config;
  const switchType = config?.switchType;
  const cases = config?.cases || [];

  return (
    <div className="relative">
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 border-2 border-gray-600 bg-gray-800"
        style={{ left: -6 }}
      />

      {/* Node Body */}
      <div className="min-w-[200px] rounded-lg border-2 border-indigo-500 bg-indigo-900/20 backdrop-blur-sm shadow-lg">
        <div className="px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-indigo-500/20 to-indigo-600/10">
          <div className="p-2 rounded-lg bg-indigo-500/20 text-indigo-500">
            <BoltIcon className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <div className="font-medium text-white text-sm">
              {data.label}
            </div>
            <div className="text-xs text-gray-400 mt-1">
              Route to different paths
            </div>
          </div>
          <div className="w-2 h-2 bg-indigo-500 rounded-full" />
        </div>

        <div className="px-4 py-3 border-t border-gray-700/50 space-y-3">
          {switchType ? (
            <div className="space-y-2">
              <div className="text-sm text-gray-300">
                Type: {switchType}
              </div>
              {cases.length > 0 && (
                <div className="space-y-1">
                  <div className="text-xs text-gray-400">Cases:</div>
                  {cases.slice(0, 3).map((caseItem: any, index: number) => (
                    <div key={index} className="text-xs bg-indigo-900/30 text-indigo-300 px-2 py-0.5 rounded">
                      {caseItem.label || `Case ${index + 1}`}
                    </div>
                  ))}
                  {cases.length > 3 && (
                    <div className="text-xs text-gray-400">
                      +{cases.length - 3} more cases
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-sm text-gray-300">
                Switch Router
              </div>
              <div className="text-xs text-gray-400">
                Route workflow to different paths based on input values or conditions.
              </div>
              <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
                ⚠️ Needs configuration
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Multiple Output Handles */}
      {cases.length > 0 ? (
        cases.slice(0, 4).map((caseItem: any, index: number) => (
          <Handle
            key={index}
            type="source"
            position={Position.Right}
            id={`case-${index}`}
            className="w-3 h-3 border-2 border-indigo-500 bg-indigo-600"
            style={{ right: -6, top: `${30 + index * 15}%` }}
          />
        ))
      ) : (
        <Handle
          type="source"
          position={Position.Right}
          className="w-3 h-3 border-2 border-gray-600 bg-gray-800"
          style={{ right: -6 }}
        />
      )}
    </div>
  );
}
