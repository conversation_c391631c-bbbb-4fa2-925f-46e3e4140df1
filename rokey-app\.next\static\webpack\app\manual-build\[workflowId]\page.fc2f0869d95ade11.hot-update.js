"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx":
/*!*********************************************************!*\
  !*** ./src/components/manual-build/NodeConfigPanel.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodeConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction NodeConfigPanel(param) {\n    let { node, onUpdate, onClose } = param;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.data.config);\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch models from database\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                console.error('Error fetching models:', err);\n                setFetchProviderModelsError(err.message);\n                setFetchedProviderModels([]);\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\"], []);\n    // Load models on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider') {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        node.type,\n        fetchModelsFromDatabase\n    ]);\n    const handleConfigChange = (key, value)=>{\n        const newConfig = {\n            ...config,\n            [key]: value\n        };\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    // Model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels && node.type === 'provider') {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).map({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"NodeConfigPanel.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    const isNodeConfigured = (nodeType, nodeConfig)=>{\n        switch(nodeType){\n            case 'provider':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'roleAgent':\n                return !!(nodeConfig.roleId && nodeConfig.roleName);\n            case 'conditional':\n                return !!(nodeConfig.condition && nodeConfig.conditionType);\n            case 'tool':\n                return !!nodeConfig.toolType;\n            case 'memory':\n                return !!(nodeConfig.memoryType && nodeConfig.storageKey);\n            case 'switch':\n                var _nodeConfig_cases;\n                return !!(nodeConfig.switchType && ((_nodeConfig_cases = nodeConfig.cases) === null || _nodeConfig_cases === void 0 ? void 0 : _nodeConfig_cases.length) > 0);\n            case 'loop':\n                return !!nodeConfig.loopType;\n            default:\n                return true;\n        }\n    };\n    const renderProviderConfig = ()=>{\n        var _providerConfig_parameters, _providerConfig_parameters1;\n        const providerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: providerConfig.providerId || '',\n                            onChange: (e)=>handleConfigChange('providerId', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"openai\",\n                                    children: \"OpenAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"anthropic\",\n                                    children: \"Anthropic\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"google\",\n                                    children: \"Google\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"deepseek\",\n                                    children: \"DeepSeek\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"xai\",\n                                    children: \"xAI (Grok)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"openrouter\",\n                                    children: \"OpenRouter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                providerConfig.providerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: providerConfig.modelId || '',\n                            onChange: (e)=>handleConfigChange('modelId', e.target.value),\n                            placeholder: \"e.g., gpt-4, claude-3-opus\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Temperature\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: ((_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>handleConfigChange('parameters', {\n                                            ...providerConfig.parameters,\n                                            temperature: parseFloat(e.target.value)\n                                        }),\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Max Tokens\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"1\",\n                                    value: ((_providerConfig_parameters1 = providerConfig.parameters) === null || _providerConfig_parameters1 === void 0 ? void 0 : _providerConfig_parameters1.maxTokens) || '',\n                                    onChange: (e)=>handleConfigChange('parameters', {\n                                            ...providerConfig.parameters,\n                                            maxTokens: parseInt(e.target.value)\n                                        }),\n                                    placeholder: \"Auto\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    };\n    const renderRoleAgentConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Role Name\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.roleName || '',\n                            onChange: (e)=>handleConfigChange('roleName', e.target.value),\n                            placeholder: \"e.g., Coder, Writer, Analyst\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Custom Prompt\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: config.customPrompt || '',\n                            onChange: (e)=>handleConfigChange('customPrompt', e.target.value),\n                            placeholder: \"Enter custom instructions for this role...\",\n                            rows: 4,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: config.memoryEnabled || false,\n                                onChange: (e)=>handleConfigChange('memoryEnabled', e.target.checked),\n                                className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-300\",\n                                children: \"Enable memory\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConditionalConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: config.conditionType || '',\n                            onChange: (e)=>handleConfigChange('conditionType', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"contains\",\n                                    children: \"Contains\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"equals\",\n                                    children: \"Equals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"regex\",\n                                    children: \"Regex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"length\",\n                                    children: \"Length\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"custom\",\n                                    children: \"Custom\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.condition || '',\n                            onChange: (e)=>handleConfigChange('condition', e.target.value),\n                            placeholder: \"Enter condition...\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"True Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.trueLabel || '',\n                                    onChange: (e)=>handleConfigChange('trueLabel', e.target.value),\n                                    placeholder: \"Continue\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"False Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.falseLabel || '',\n                                    onChange: (e)=>handleConfigChange('falseLabel', e.target.value),\n                                    placeholder: \"Skip\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this);\n    };\n    const renderDefaultConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: node.data.label,\n                            onChange: (e)=>onUpdate({\n                                    label: e.target.value\n                                }),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: node.data.description || '',\n                            onChange: (e)=>onUpdate({\n                                    description: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 333,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConfigContent = ()=>{\n        switch(node.type){\n            case 'provider':\n                return renderProviderConfig();\n            case 'roleAgent':\n                return renderRoleAgentConfig();\n            case 'conditional':\n                return renderConditionalConfig();\n            default:\n                return renderDefaultConfig();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5 text-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Configure Node\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: node.data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderConfigContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 rounded-lg border border-gray-700/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: node.data.isConfigured ? 'Configured' : 'Needs Configuration'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: node.data.isConfigured ? 'This node is properly configured and ready to use.' : 'Complete the configuration to use this node in your workflow.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, this);\n}\n_s(NodeConfigPanel, \"hoRKQcjeYT6L8h8tdKK/ym4mAP4=\");\n_c2 = NodeConfigPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"NodeConfigPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\n"));

/***/ })

});