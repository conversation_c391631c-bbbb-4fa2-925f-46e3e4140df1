var e="-ms-";var r="-moz-";var a="-webkit-";var c="comm";var n="rule";var s="decl";var t="@page";var u="@media";var i="@import";var f="@charset";var o="@viewport";var l="@supports";var p="@document";var v="@namespace";var b="@keyframes";var h="@font-face";var w="@counter-style";var d="@font-feature-values";var g="@layer";var k="@scope";var $=Math.abs;var m=String.fromCharCode;var x=Object.assign;function y(e,r){return A(e,0)^45?(((r<<2^A(e,0))<<2^A(e,1))<<2^A(e,2))<<2^A(e,3):0}function j(e){return e.trim()}function z(e,r){return(e=r.exec(e))?e[0]:e}function C(e,r,a){return e.replace(r,a)}function O(e,r,a){return e.indexOf(r,a)}function A(e,r){return e.charCodeAt(r)|0}function M(e,r,a){return e.slice(r,a)}function S(e){return e.length}function q(e){return e.length}function B(e,r){return r.push(e),e}function D(e,r){return e.map(r).join("")}function E(e,r){return e.filter((function(e){return!z(e,r)}))}var F=1;var G=1;var H=0;var I=0;var J=0;var K="";function L(e,r,a,c,n,s,t,u){return{value:e,root:r,parent:a,type:c,props:n,children:s,line:F,column:G,length:t,return:"",siblings:u}}function N(e,r){return x(L("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},r)}function P(e){while(e.root)e=N(e.root,{children:[e]});B(e,e.siblings)}function Q(){return J}function R(){J=I>0?A(K,--I):0;if(G--,J===10)G=1,F--;return J}function T(){J=I<H?A(K,I++):0;if(G++,J===10)G=1,F++;return J}function U(){return A(K,I)}function V(){return I}function W(e,r){return M(K,e,r)}function X(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Y(e){return F=G=1,H=S(K=e),I=0,[]}function Z(e){return K="",e}function _(e){return j(W(I-1,ne(e===91?e+2:e===40?e+1:e)))}function ee(e){return Z(ae(Y(e)))}function re(e){while(J=U())if(J<33)T();else break;return X(e)>2||X(J)>3?"":" "}function ae(e){while(T())switch(X(J)){case 0:B(te(I-1),e);break;case 2:B(_(J),e);break;default:B(m(J),e)}return e}function ce(e,r){while(--r&&T())if(J<48||J>102||J>57&&J<65||J>70&&J<97)break;return W(e,V()+(r<6&&U()==32&&T()==32))}function ne(e){while(T())switch(J){case e:return I;case 34:case 39:if(e!==34&&e!==39)ne(J);break;case 40:if(e===41)ne(e);break;case 92:T();break}return I}function se(e,r){while(T())if(e+J===47+10)break;else if(e+J===42+42&&U()===47)break;return"/*"+W(r,I-1)+"*"+m(e===47?e:T())}function te(e){while(!X(U()))T();return W(e,I)}function ue(e){return Z(ie("",null,null,null,[""],e=Y(e),0,[0],e))}function ie(e,r,a,c,n,s,t,u,i){var f=0;var o=0;var l=t;var p=0;var v=0;var b=0;var h=1;var w=1;var d=1;var g=0;var k="";var x=n;var y=s;var j=c;var z=k;while(w)switch(b=g,g=T()){case 40:if(b!=108&&A(z,l-1)==58){if(O(z+=C(_(g),"&","&\f"),"&\f",$(f?u[f-1]:0))!=-1)d=-1;break}case 34:case 39:case 91:z+=_(g);break;case 9:case 10:case 13:case 32:z+=re(b);break;case 92:z+=ce(V()-1,7);continue;case 47:switch(U()){case 42:case 47:B(oe(se(T(),V()),r,a,i),i);if((X(b||1)==5||X(U()||1)==5)&&S(z)&&M(z,-1,void 0)!==" ")z+=" ";break;default:z+="/"}break;case 123*h:u[f++]=S(z)*d;case 125*h:case 59:case 0:switch(g){case 0:case 125:w=0;case 59+o:if(d==-1)z=C(z,/\f/g,"");if(v>0&&(S(z)-l||h===0&&b===47))B(v>32?le(z+";",c,a,l-1,i):le(C(z," ","")+";",c,a,l-2,i),i);break;case 59:z+=";";default:B(j=fe(z,r,a,f,o,n,u,k,x=[],y=[],l,s),s);if(g===123)if(o===0)ie(z,r,j,j,x,s,l,u,y);else{switch(p){case 99:if(A(z,3)===110)break;case 108:if(A(z,2)===97)break;default:o=0;case 100:case 109:case 115:}if(o)ie(e,j,j,c&&B(fe(e,j,j,0,0,n,u,k,n,x=[],l,y),y),n,y,l,u,c?x:y);else ie(z,j,j,j,[""],y,0,u,y)}}f=o=v=0,h=d=1,k=z="",l=t;break;case 58:l=1+S(z),v=b;default:if(h<1)if(g==123)--h;else if(g==125&&h++==0&&R()==125)continue;switch(z+=m(g),g*h){case 38:d=o>0?1:(z+="\f",-1);break;case 44:u[f++]=(S(z)-1)*d,d=1;break;case 64:if(U()===45)z+=_(T());p=U(),o=l=S(k=z+=te(V())),g++;break;case 45:if(b===45&&S(z)==2)h=0}}return s}function fe(e,r,a,c,s,t,u,i,f,o,l,p){var v=s-1;var b=s===0?t:[""];var h=q(b);for(var w=0,d=0,g=0;w<c;++w)for(var k=0,m=M(e,v+1,v=$(d=u[w])),x=e;k<h;++k)if(x=j(d>0?b[k]+" "+m:C(m,/&\f/g,b[k])))f[g++]=x;return L(e,r,a,s===0?n:i,f,o,l,p)}function oe(e,r,a,n){return L(e,r,a,c,m(Q()),M(e,2,-2),0,n)}function le(e,r,a,c,n){return L(e,r,a,s,M(e,0,c),M(e,c+1,-1),c,n)}function pe(c,n,s){switch(y(c,n)){case 5103:return a+"print-"+c+c;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:case 6391:case 5879:case 5623:case 6135:case 4599:return a+c+c;case 4855:return a+c.replace("add","source-over").replace("substract","source-out").replace("intersect","source-in").replace("exclude","xor")+c;case 4789:return r+c+c;case 5349:case 4246:case 4810:case 6968:case 2756:return a+c+r+c+e+c+c;case 5936:switch(A(c,n+11)){case 114:return a+c+e+C(c,/[svh]\w+-[tblr]{2}/,"tb")+c;case 108:return a+c+e+C(c,/[svh]\w+-[tblr]{2}/,"tb-rl")+c;case 45:return a+c+e+C(c,/[svh]\w+-[tblr]{2}/,"lr")+c}case 6828:case 4268:case 2903:return a+c+e+c+c;case 6165:return a+c+e+"flex-"+c+c;case 5187:return a+c+C(c,/(\w+).+(:[^]+)/,a+"box-$1$2"+e+"flex-$1$2")+c;case 5443:return a+c+e+"flex-item-"+C(c,/flex-|-self/g,"")+(!z(c,/flex-|baseline/)?e+"grid-row-"+C(c,/flex-|-self/g,""):"")+c;case 4675:return a+c+e+"flex-line-pack"+C(c,/align-content|flex-|-self/g,"")+c;case 5548:return a+c+e+C(c,"shrink","negative")+c;case 5292:return a+c+e+C(c,"basis","preferred-size")+c;case 6060:return a+"box-"+C(c,"-grow","")+a+c+e+C(c,"grow","positive")+c;case 4554:return a+C(c,/([^-])(transform)/g,"$1"+a+"$2")+c;case 6187:return C(C(C(c,/(zoom-|grab)/,a+"$1"),/(image-set)/,a+"$1"),c,"")+c;case 5495:case 3959:return C(c,/(image-set\([^]*)/,a+"$1"+"$`$1");case 4968:return C(C(c,/(.+:)(flex-)?(.*)/,a+"box-pack:$3"+e+"flex-pack:$3"),/space-between/,"justify")+a+c+c;case 4200:if(!z(c,/flex-|baseline/))return e+"grid-column-align"+M(c,n)+c;break;case 2592:case 3360:return e+C(c,"template-","")+c;case 4384:case 3616:if(s&&s.some((function(e,r){return n=r,z(e.props,/grid-\w+-end/)}))){return~O(c+(s=s[n].value),"span",0)?c:e+C(c,"-start","")+c+e+"grid-row-span:"+(~O(s,"span",0)?z(s,/\d+/):+z(s,/\d+/)-+z(c,/\d+/))+";"}return e+C(c,"-start","")+c;case 4896:case 4128:return s&&s.some((function(e){return z(e.props,/grid-\w+-start/)}))?c:e+C(C(c,"-end","-span"),"span ","")+c;case 4095:case 3583:case 4068:case 2532:return C(c,/(.+)-inline(.+)/,a+"$1$2")+c;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(S(c)-1-n>6)switch(A(c,n+1)){case 109:if(A(c,n+4)!==45)break;case 102:return C(c,/(.+:)(.+)-([^]+)/,"$1"+a+"$2-$3"+"$1"+r+(A(c,n+3)==108?"$3":"$2-$3"))+c;case 115:return~O(c,"stretch",0)?pe(C(c,"stretch","fill-available"),n,s)+c:c}break;case 5152:case 5920:return C(c,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,(function(r,a,n,s,t,u,i){return e+a+":"+n+i+(s?e+a+"-span:"+(t?u:+u-+n)+i:"")+c}));case 4949:if(A(c,n+6)===121)return C(c,":",":"+a)+c;break;case 6444:switch(A(c,A(c,14)===45?18:11)){case 120:return C(c,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+a+(A(c,14)===45?"inline-":"")+"box$3"+"$1"+a+"$2$3"+"$1"+e+"$2box$3")+c;case 100:return C(c,":",":"+e)+c}break;case 5719:case 2647:case 2135:case 3927:case 2391:return C(c,"scroll-","scroll-snap-")+c}return c}function ve(e,r){var a="";for(var c=0;c<e.length;c++)a+=r(e[c],c,e,r)||"";return a}function be(e,r,a,t){switch(e.type){case g:if(e.children.length)break;case i:case v:case s:return e.return=e.return||e.value;case c:return"";case b:return e.return=e.value+"{"+ve(e.children,t)+"}";case n:if(!S(e.value=e.props.join(",")))return""}return S(a=ve(e.children,t))?e.return=e.value+"{"+a+"}":""}function he(e){var r=q(e);return function(a,c,n,s){var t="";for(var u=0;u<r;u++)t+=e[u](a,c,n,s)||"";return t}}function we(e){return function(r){if(!r.root)if(r=r.return)e(r)}}function de(c,t,u,i){if(c.length>-1)if(!c.return)switch(c.type){case s:c.return=pe(c.value,c.length,u);return;case b:return ve([N(c,{value:C(c.value,"@","@"+a)})],i);case n:if(c.length)return D(u=c.props,(function(n){switch(z(n,i=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":P(N(c,{props:[C(n,/:(read-\w+)/,":"+r+"$1")]}));P(N(c,{props:[n]}));x(c,{props:E(u,i)});break;case"::placeholder":P(N(c,{props:[C(n,/:(plac\w+)/,":"+a+"input-$1")]}));P(N(c,{props:[C(n,/:(plac\w+)/,":"+r+"$1")]}));P(N(c,{props:[C(n,/:(plac\w+)/,e+"input-$1")]}));P(N(c,{props:[n]}));x(c,{props:E(u,i)});break}return""}))}}function ge(e){switch(e.type){case n:e.props=e.props.map((function(r){return D(ee(r),(function(r,a,c){switch(A(r,0)){case 12:return M(r,1,S(r));case 0:case 40:case 43:case 62:case 126:return r;case 58:if(c[++a]==="global")c[a]="",c[++a]="\f"+M(c[a],a=1,-1);case 32:return a===1?"":r;default:switch(a){case 0:e=r;return q(c)>1?"":r;case a=q(c)-1:case 2:return a===2?r+e+e:r+e;default:return r}}}))}))}}export{f as CHARSET,c as COMMENT,w as COUNTER_STYLE,s as DECLARATION,p as DOCUMENT,h as FONT_FACE,d as FONT_FEATURE_VALUES,i as IMPORT,b as KEYFRAMES,g as LAYER,u as MEDIA,r as MOZ,e as MS,v as NAMESPACE,t as PAGE,n as RULESET,k as SCOPE,l as SUPPORTS,o as VIEWPORT,a as WEBKIT,$ as abs,Y as alloc,B as append,x as assign,V as caret,Q as char,J as character,K as characters,A as charat,G as column,D as combine,oe as comment,se as commenter,ue as compile,N as copy,Z as dealloc,le as declaration,_ as delimit,ne as delimiter,ce as escaping,E as filter,m as from,y as hash,te as identifier,O as indexof,H as length,P as lift,F as line,z as match,he as middleware,ge as namespace,T as next,L as node,ie as parse,U as peek,I as position,pe as prefix,de as prefixer,R as prev,C as replace,fe as ruleset,we as rulesheet,ve as serialize,q as sizeof,W as slice,be as stringify,S as strlen,M as substr,X as token,ee as tokenize,ae as tokenizer,j as trim,re as whitespace};
//# sourceMappingURL=stylis.mjs.map
