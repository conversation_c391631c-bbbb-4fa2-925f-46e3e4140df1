{"name": "react-hotkeys-hook", "description": "React hook for handling keyboard shortcuts", "version": "5.1.0", "repository": {"type": "git", "url": "https://github.com/JohannesKlauss/react-keymap-hook.git"}, "homepage": "https://johannesklauss.github.io/react-hotkeys-hook/", "author": "<PERSON>", "type": "module", "main": "packages/react-hotkeys-hook/dist/index.js", "types": "packages/react-hotkeys-hook/dist/index.d.ts", "files": ["packages/react-hotkeys-hook/dist"], "keywords": ["react", "hook", "hooks", "component", "hotkey", "shortcut", "keyboard", "shortcuts", "keypress", "hotkeys"], "license": "MIT", "scripts": {"build": "npm run -w packages/react-hotkeys-hook build", "build:documentation": "npm run -w packages/documentation build", "lint": "npm run -w packages/react-hotkeys-hook lint", "test": "npm run -w packages/react-hotkeys-hook test", "prepublishOnly": "npm run test && npm run lint && npm run build"}, "workspaces": ["packages/*"], "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}