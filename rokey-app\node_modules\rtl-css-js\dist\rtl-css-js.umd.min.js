!function(r,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(r=r||self).RtlCssJs=t()}(this,(function(){"use strict";function r(r){return r.reduce((function(r,t){var e=t[0],o=t[1];return r[e]=o,r[o]=e,r}),{})}function t(r){return"number"==typeof r}function e(r,t){return-1!==r.indexOf(t)}function o(r,t,e,o){return t+(n=e,0===parseFloat(n)?n:"-"===n[0]?n.slice(1):"-"+n)+o;var n}function n(r){return r.replace(/ +/g," ").split(" ").map((function(r){return r.trim()})).filter(Boolean).reduce((function(r,t){var e=r.list,o=r.state,n=(t.match(/\(/g)||[]).length,i=(t.match(/\)/g)||[]).length;return o.parensDepth>0?e[e.length-1]=e[e.length-1]+" "+t:e.push(t),o.parensDepth+=n-i,{list:e,state:o}}),{list:[],state:{parensDepth:0}}).list}function i(r){var t=n(r);if(t.length<=3||t.length>4)return r;var e=t[0],o=t[1],i=t[2];return[e,t[3],i,o].join(" ")}var a={padding:function(r){var e=r.value;return t(e)?e:i(e)},textShadow:function(r){return function(r){for(var t=[],e=0,o=0,n=!1;o<r.length;)n||","!==r[o]?"("===r[o]?(n=!0,o++):")"===r[o]?(n=!1,o++):o++:(t.push(r.substring(e,o).trim()),e=++o);return e!=o&&t.push(r.substring(e,o+1)),t}(r.value).map((function(r){return r.replace(/(^|\s)(-*)([.|\d]+)/,(function(r,t,e,o){return"0"===o?r:""+t+(""===e?"-":"")+o}))})).join(",")},borderColor:function(r){return i(r.value)},borderRadius:function(r){var o=r.value;if(t(o))return o;if(e(o,"/")){var i=o.split("/"),s=i[0],u=i[1];return a.borderRadius({value:s.trim()})+" / "+a.borderRadius({value:u.trim()})}var d=n(o);switch(d.length){case 2:return d.reverse().join(" ");case 4:var g=d[0],l=d[1],f=d[2];return[l,g,d[3],f].join(" ");default:return o}},background:function(r){var e=r.value,o=r.valuesToConvert,n=r.isRtl,i=r.bgImgDirectionRegex,s=r.bgPosDirectionRegex;if(t(e))return e;var u=e.replace(/(url\(.*?\))|(rgba?\(.*?\))|(hsl\(.*?\))|(#[a-fA-F0-9]+)|((^| )(\D)+( |$))/g,"").trim();return e=e.replace(u,a.backgroundPosition({value:u,valuesToConvert:o,isRtl:n,bgPosDirectionRegex:s})),a.backgroundImage({value:e,valuesToConvert:o,bgImgDirectionRegex:i})},backgroundImage:function(r){var t=r.value,o=r.valuesToConvert,n=r.bgImgDirectionRegex;return e(t,"url(")||e(t,"linear-gradient(")?t.replace(n,(function(r,t,e){return r.replace(e,o[e])})):t},backgroundPosition:function(r){var t=r.value,e=r.valuesToConvert,o=r.isRtl,n=r.bgPosDirectionRegex;return t.replace(o?/^((-|\d|\.)+%)/:null,(function(r,t){return function(r){var t=r.indexOf(".");if(-1===t)r=100-parseFloat(r)+"%";else{var e=r.length-t-2;r=(r=100-parseFloat(r)).toFixed(e)+"%"}return r}(t)})).replace(n,(function(r){return e[r]}))},backgroundPositionX:function(r){var e=r.value,o=r.valuesToConvert,n=r.isRtl,i=r.bgPosDirectionRegex;return t(e)?e:a.backgroundPosition({value:e,valuesToConvert:o,isRtl:n,bgPosDirectionRegex:i})},transition:function(r){var t=r.value,e=r.propertiesToConvert;return t.split(/,\s*/g).map((function(r){var t=r.split(" ");return t[0]=e[t[0]]||t[0],t.join(" ")})).join(", ")},transitionProperty:function(r){var t=r.value,e=r.propertiesToConvert;return t.split(/,\s*/g).map((function(r){return e[r]||r})).join(", ")},transform:function(r){var t=r.value,e="(?:(?:(?:\\[0-9a-f]{1,6})(?:\\r\\n|\\s)?)|\\\\[^\\r\\n\\f0-9a-f])",n="((?:-?(?:[0-9]*\\.[0-9]+|[0-9]+)(?:\\s*(?:em|ex|px|cm|mm|in|pt|pc|deg|rad|grad|ms|s|hz|khz|%)|-?(?:[_a-z]|[^\\u0020-\\u007e]|"+e+")(?:[_a-z0-9-]|[^\\u0020-\\u007e]|"+e+")*)?)|(?:inherit|auto))",i=new RegExp("(translateX\\s*\\(\\s*)"+n+"(\\s*\\))","gi"),a=new RegExp("(translate\\s*\\(\\s*)"+n+"((?:\\s*,\\s*"+n+"){0,1}\\s*\\))","gi"),s=new RegExp("(translate3d\\s*\\(\\s*)"+n+"((?:\\s*,\\s*"+n+"){0,2}\\s*\\))","gi"),u=new RegExp("(rotate[ZY]?\\s*\\(\\s*)"+n+"(\\s*\\))","gi");return t.replace(i,o).replace(a,o).replace(s,o).replace(u,o)}};a.objectPosition=a.backgroundPosition,a.margin=a.padding,a.borderWidth=a.padding,a.boxShadow=a.textShadow,a.webkitBoxShadow=a.boxShadow,a.mozBoxShadow=a.boxShadow,a.WebkitBoxShadow=a.boxShadow,a.MozBoxShadow=a.boxShadow,a.borderStyle=a.borderColor,a.webkitTransform=a.transform,a.mozTransform=a.transform,a.WebkitTransform=a.transform,a.MozTransform=a.transform,a.transformOrigin=a.backgroundPosition,a.webkitTransformOrigin=a.transformOrigin,a.mozTransformOrigin=a.transformOrigin,a.WebkitTransformOrigin=a.transformOrigin,a.MozTransformOrigin=a.transformOrigin,a.webkitTransition=a.transition,a.mozTransition=a.transition,a.WebkitTransition=a.transition,a.MozTransition=a.transition,a.webkitTransitionProperty=a.transitionProperty,a.mozTransitionProperty=a.transitionProperty,a.WebkitTransitionProperty=a.transitionProperty,a.MozTransitionProperty=a.transitionProperty,a["text-shadow"]=a.textShadow,a["border-color"]=a.borderColor,a["border-radius"]=a.borderRadius,a["background-image"]=a.backgroundImage,a["background-position"]=a.backgroundPosition,a["background-position-x"]=a.backgroundPositionX,a["object-position"]=a.objectPosition,a["border-width"]=a.padding,a["box-shadow"]=a.textShadow,a["-webkit-box-shadow"]=a.textShadow,a["-moz-box-shadow"]=a.textShadow,a["border-style"]=a.borderColor,a["-webkit-transform"]=a.transform,a["-moz-transform"]=a.transform,a["transform-origin"]=a.transformOrigin,a["-webkit-transform-origin"]=a.transformOrigin,a["-moz-transform-origin"]=a.transformOrigin,a["-webkit-transition"]=a.transition,a["-moz-transition"]=a.transition,a["transition-property"]=a.transitionProperty,a["-webkit-transition-property"]=a.transitionProperty,a["-moz-transition-property"]=a.transitionProperty;var s=r([["paddingLeft","paddingRight"],["marginLeft","marginRight"],["left","right"],["borderLeft","borderRight"],["borderLeftColor","borderRightColor"],["borderLeftStyle","borderRightStyle"],["borderLeftWidth","borderRightWidth"],["borderTopLeftRadius","borderTopRightRadius"],["borderBottomLeftRadius","borderBottomRightRadius"],["padding-left","padding-right"],["margin-left","margin-right"],["border-left","border-right"],["border-left-color","border-right-color"],["border-left-style","border-right-style"],["border-left-width","border-right-width"],["border-top-left-radius","border-top-right-radius"],["border-bottom-left-radius","border-bottom-right-radius"]]),u=["content"],d=r([["ltr","rtl"],["left","right"],["w-resize","e-resize"],["sw-resize","se-resize"],["nw-resize","ne-resize"]]),g=new RegExp("(^|\\W|_)((ltr)|(rtl)|(left)|(right))(\\W|_|$)","g"),l=new RegExp("(left)|(right)");function f(r){return Object.keys(r).reduce((function(o,n){var i=r[n];if("string"==typeof i&&(i=i.trim()),e(u,n))return o[n]=i,o;var c=function(r,e){var o=/\/\*\s?@noflip\s?\*\//.test(e),n=o?r:(u=r,s[u]||u),i=o?e:function(r,e){if(!function(r){return!(t=r,"boolean"==typeof t||function(r){return null==r}(r));var t}(e))return e;if(function(r){return r&&"object"==typeof r}(e))return f(e);var o,n=t(e),i=function(r){return"function"==typeof r}(e),u=n||i?e:e.replace(/ !important.*?$/,""),c=!n&&u.length!==e.length,b=a[r];o=b?b({value:u,valuesToConvert:d,propertiesToConvert:s,isRtl:!0,bgImgDirectionRegex:g,bgPosDirectionRegex:l}):d[u]||u;if(c)return o+" !important";return o}(n,e);var u;return{key:n,value:i}}(n,i),b=c.key,p=c.value;return o[b]=p,o}),Array.isArray(r)?[]:{})}return f}));
//# sourceMappingURL=rtl-css-js.umd.min.js.map
