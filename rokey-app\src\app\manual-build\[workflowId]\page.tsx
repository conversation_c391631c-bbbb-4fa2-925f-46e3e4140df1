'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ReactFlow, Background, Controls, MiniMap, useNodesState, useEdgesState, addEdge, Connection, Edge } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { WorkflowNode, WorkflowEdge, ManualBuildWorkflow } from '@/types/manualBuild';
import WorkflowToolbar from '@/components/manual-build/WorkflowToolbar';
import NodePalette from '@/components/manual-build/NodePalette';
import NodeConfigPanel from '@/components/manual-build/NodeConfigPanel';
import { nodeTypes } from '@/components/manual-build/nodes';

interface WorkflowEditorPageProps {
  params: Promise<{ workflowId: string }>;
}

export default function WorkflowEditorPage({ params }: WorkflowEditorPageProps) {
  const resolvedParams = useParams();
  const router = useRouter();
  const workflowId = resolvedParams?.workflowId as string;
  
  const [workflow, setWorkflow] = useState<ManualBuildWorkflow | null>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isDirty, setIsDirty] = useState(false);

  // Load workflow data
  useEffect(() => {
    if (workflowId === 'new') {
      initializeNewWorkflow();
    } else {
      loadWorkflow(workflowId);
    }
  }, [workflowId]);

  const initializeNewWorkflow = async () => {
    try {
      // Create default nodes for new workflow
      const defaultNodes: WorkflowNode[] = [
        {
          id: 'user-request',
          type: 'userRequest',
          position: { x: 100, y: 100 },
          data: {
            label: 'User Request',
            config: {},
            isConfigured: true,
            description: 'Starting point for user input'
          }
        },
        {
          id: 'classifier',
          type: 'classifier',
          position: { x: 350, y: 100 },
          data: {
            label: 'Classifier',
            config: {},
            isConfigured: true,
            description: 'Analyzes and categorizes the request'
          }
        },
        {
          id: 'output',
          type: 'output',
          position: { x: 600, y: 100 },
          data: {
            label: 'Output',
            config: {},
            isConfigured: true,
            description: 'Final response to the user'
          }
        }
      ];

      const defaultEdges: WorkflowEdge[] = [
        {
          id: 'e1',
          source: 'user-request',
          target: 'classifier',
          type: 'smoothstep',
          animated: true
        }
      ];

      setNodes(defaultNodes);
      setEdges(defaultEdges);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to initialize new workflow:', error);
      setIsLoading(false);
    }
  };

  const loadWorkflow = async (id: string) => {
    try {
      // TODO: Implement API call to load workflow
      console.log('Loading workflow:', id);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to load workflow:', error);
      setIsLoading(false);
    }
  };

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge: Edge = {
        ...params,
        id: `e${edges.length + 1}`,
        type: 'smoothstep',
        animated: true
      };
      setEdges((eds) => addEdge(newEdge, eds));
      setIsDirty(true);
    },
    [edges.length, setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: WorkflowNode) => {
    setSelectedNode(node);
  }, []);

  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
  }, []);

  const handleSave = async () => {
    if (!workflow && workflowId === 'new') {
      // Show save dialog for new workflow
      const name = prompt('Enter workflow name:');
      if (!name) return;
      
      // TODO: Implement save new workflow
      console.log('Saving new workflow:', name);
    } else {
      // Update existing workflow
      setIsSaving(true);
      try {
        // TODO: Implement update workflow API call
        console.log('Updating workflow:', workflowId);
        setIsDirty(false);
      } catch (error) {
        console.error('Failed to save workflow:', error);
      } finally {
        setIsSaving(false);
      }
    }
  };

  const handleExecute = async () => {
    // TODO: Implement workflow execution
    console.log('Executing workflow');
  };

  const handleAddNode = (nodeType: string, position: { x: number; y: number }) => {
    const newNode: WorkflowNode = {
      id: `${nodeType}-${Date.now()}`,
      type: nodeType as any,
      position,
      data: {
        label: nodeType.charAt(0).toUpperCase() + nodeType.slice(1),
        config: {},
        isConfigured: false,
        description: `${nodeType} node`
      }
    };

    setNodes((nds) => [...nds, newNode]);
    setIsDirty(true);
  };

  const handleNodeUpdate = (nodeId: string, updates: Partial<WorkflowNode['data']>) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...updates } }
          : node
      )
    );
    setIsDirty(true);
  };

  if (isLoading) {
    return (
      <div className="h-screen bg-[#040716] flex items-center justify-center">
        <div className="text-white">Loading workflow...</div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-[#040716] flex flex-col">
      {/* Toolbar */}
      <WorkflowToolbar
        workflow={workflow}
        isDirty={isDirty}
        isSaving={isSaving}
        onSave={handleSave}
        onExecute={handleExecute}
        onBack={() => router.push('/manual-build')}
      />

      <div className="flex-1 flex">
        {/* Node Palette */}
        <NodePalette onAddNode={handleAddNode} />

        {/* Main Canvas */}
        <div className="flex-1 relative">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onPaneClick={onPaneClick}
            nodeTypes={nodeTypes}
            fitView
            className="bg-[#040716]"
            defaultViewport={{ x: 0, y: 0, zoom: 1 }}
          >
            <Background 
              color="#1f2937" 
              gap={20} 
              size={1}
              variant="dots"
            />
            <Controls 
              className="bg-gray-800 border border-gray-700"
              showInteractive={false}
            />
            <MiniMap 
              className="bg-gray-800 border border-gray-700"
              nodeColor="#ff6b35"
              maskColor="rgba(0, 0, 0, 0.2)"
            />
          </ReactFlow>
        </div>

        {/* Configuration Panel */}
        {selectedNode && (
          <NodeConfigPanel
            node={selectedNode}
            onUpdate={(updates) => handleNodeUpdate(selectedNode.id, updates)}
            onClose={() => setSelectedNode(null)}
          />
        )}
      </div>
    </div>
  );
}
