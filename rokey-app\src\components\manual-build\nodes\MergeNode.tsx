'use client';

import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { NodeProps, Handle, Position } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode } from '@/types/manualBuild';

export default function MergeNode({ data }: NodeProps<WorkflowNode['data']>) {
  return (
    <div className="relative">
      {/* Multiple Input Handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="input1"
        className="w-3 h-3 border-2 border-gray-600 bg-gray-800"
        style={{ left: -6, top: '30%' }}
      />
      <Handle
        type="target"
        position={Position.Left}
        id="input2"
        className="w-3 h-3 border-2 border-gray-600 bg-gray-800"
        style={{ left: -6, top: '50%' }}
      />
      <Handle
        type="target"
        position={Position.Left}
        id="input3"
        className="w-3 h-3 border-2 border-gray-600 bg-gray-800"
        style={{ left: -6, top: '70%' }}
      />

      {/* Node Body */}
      <div className="min-w-[200px] rounded-lg border-2 border-cyan-500 bg-cyan-900/20 backdrop-blur-sm shadow-lg">
        <div className="px-4 py-3 rounded-t-lg flex items-center gap-3 bg-gradient-to-r from-cyan-500/20 to-cyan-600/10">
          <div className="p-2 rounded-lg bg-cyan-500/20 text-cyan-500">
            <ArrowPathIcon className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <div className="font-medium text-white text-sm">
              {data.label}
            </div>
            <div className="text-xs text-gray-400 mt-1">
              Combine multiple inputs
            </div>
          </div>
          <div className="w-2 h-2 bg-cyan-500 rounded-full" />
        </div>

        <div className="px-4 py-3 border-t border-gray-700/50">
          <div className="text-xs text-gray-400">
            Merges outputs from multiple nodes into a single stream for further processing.
          </div>
        </div>
      </div>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 border-2 border-gray-600 bg-gray-800"
        style={{ right: -6 }}
      />
    </div>
  );
}
