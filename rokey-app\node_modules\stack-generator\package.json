{"name": "stack-generator", "description": "Generate artificial backtrace by walking arguments.callee.caller chain", "maintainers": ["<PERSON> <<EMAIL>> (https://www.eriwen.com)", "<PERSON> <vkhomy<PERSON><PERSON>@gmail.com> (https://github.com/victor-ho<PERSON><PERSON><PERSON>)", "<PERSON> (https://github.com/oliversalzburg)", "<PERSON> (https://github.com/bengourley)"], "version": "2.0.10", "license": "MIT", "keywords": ["stacktrace", "error"], "homepage": "https://www.stacktracejs.com", "repository": {"type": "git", "url": "git://github.com/stacktracejs/stack-generator.git"}, "dependencies": {"stackframe": "^1.3.4"}, "devDependencies": {"eslint": "^8.17.0", "jasmine": "^4.1.0", "jasmine-core": "^4.1.1", "karma": "^6.3.20", "karma-chrome-launcher": "^3.1.1", "karma-coverage": "^2.2.0", "karma-coveralls": "^2.1.0", "karma-firefox-launcher": "^2.1.2", "karma-ie-launcher": "^1.0.0", "karma-jasmine": "^4.0.2", "karma-opera-launcher": "^1.0.0", "karma-phantomjs-launcher": "^1.0.4", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-spec-reporter": "^0.0.34", "uglify-es": "^3.3.9"}, "bugs": {"url": "https://github.com/stacktracejs/stack-generator/issues"}, "main": "./stack-generator.js", "typings": "./stack-generator.d.js", "files": ["LICENSE", "README.md", "stack-generator.js", "stack-generator.d.ts", "dist/"], "scripts": {"lint": "eslint --fix stack-generator.js spec/", "test": "karma start karma.conf.js --single-run", "test-pr": "karma start karma.conf.js --single-run --browsers Firefox,Chrome_No_Sandbox", "test-ci": "karma start karma.conf.ci.js --single-run", "prepare": "cp stack-generator.js dist/ && uglifyjs node_modules/stackframe/stackframe.js stack-generator.js -o dist/stack-generator.min.js --compress --mangle --source-map \"url=stack-generator.min.js.map\""}}