{"name": "ts-easing", "version": "0.2.0", "description": "Collection of easing functions in TypeScript", "main": "lib/index.js", "files": ["lib/"], "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> lib"}, "author": "@streamich", "license": "Unlicense", "repository": {"type": "git", "url": "https://github.com/streamich/ts-easing.git"}, "bugs": {"url": "https://github.com/streamich/ts-easing/issues"}, "homepage": "https://github.com/streamich/ts-easing#readme", "dependencies": {}, "devDependencies": {"typescript": "^3.1.3", "rimraf": "^2.6.2"}}