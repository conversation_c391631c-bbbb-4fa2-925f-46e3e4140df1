## [1.9.5](https://github.com/xobotyi/scrollbar-width/compare/v1.9.4...v1.9.5) (2020-03-25)


### Bug Fixes

* fix lint errors. ([c9a711d](https://github.com/xobotyi/scrollbar-width/commit/c9a711dc4cfe83958daa7dd00c367d08a19d30e8))

## [1.9.4](https://github.com/xobotyi/scrollbar-width/compare/v1.9.3...v1.9.4) (2020-03-02)


### Bug Fixes

* **lint:** rename interface to pass linting; ([a81601b](https://github.com/xobotyi/scrollbar-width/commit/a81601be0b19427d9024d9da149859d18051c79f))

## [1.9.3](https://github.com/xobotyi/scrollbar-width/compare/v1.9.2...v1.9.3) (2020-02-24)


### Bug Fixes

* readme fix; ([35902e0](https://github.com/xobotyi/scrollbar-width/commit/35902e085f6007954b552ce96bde0c342a0b4a4e))

## [1.9.2](https://github.com/xobotyi/scrollbar-width/compare/v1.9.1...v1.9.2) (2020-02-24)


### Bug Fixes

* add clarification about the cache as promised in issue [#2](https://github.com/xobotyi/scrollbar-width/issues/2); ([944436f](https://github.com/xobotyi/scrollbar-width/commit/944436f421d01abe1bc5c17cc6ebdefd9c9d97a7))

## [1.9.1](https://github.com/xobotyi/scrollbar-width/compare/v1.9.0...v1.9.1) (2020-02-24)


### Bug Fixes

* adjusting document check for SSR ([bd68002](https://github.com/xobotyi/scrollbar-width/commit/bd68002f7b247d49a2af02342e927ef08328c998))

# [1.9.0](https://github.com/xobotyi/scrollbar-width/compare/v1.8.2...v1.9.0) (2020-02-16)


### Bug Fixes

* added blank spaces after headings; ([99da435](https://github.com/xobotyi/scrollbar-width/commit/99da435376b7a91c284e1f4dc35eff02694383d5))
* lint-staged commands been using the tslint; ([72810b9](https://github.com/xobotyi/scrollbar-width/commit/72810b9c3b039198187b2067d482d37871555247))


### Features

* terser now minifies all the code via rollup plugin; ([3596475](https://github.com/xobotyi/scrollbar-width/commit/3596475b903d0523c960a011e946480718d11382))

## [1.8.2](https://github.com/xobotyi/scrollbar-width/compare/v1.8.1...v1.8.2) (2020-01-30)


### Bug Fixes

* tweak the travis build (build missing in dist). ([77b4db3](https://github.com/xobotyi/scrollbar-width/commit/77b4db32180576e79f6c0a2d162cef25b627a0d6))

## [1.8.1](https://github.com/xobotyi/scrollbar-width/compare/v1.8.0...v1.8.1) (2020-01-30)


### Bug Fixes

* make only `dist` directory appear in package. ([adf19e6](https://github.com/xobotyi/scrollbar-width/commit/adf19e6e0338becf94f7fe5972a9659721e6ee13))

# [1.8.0](https://github.com/xobotyi/scrollbar-width/compare/v1.7.0...v1.8.0) (2020-01-27)


### Features

* rework travis config; ([a4f7ff9](https://github.com/xobotyi/scrollbar-width/commit/a4f7ff92eda332051761becab28d0be39c1f8408))

# [1.7.0](https://github.com/xobotyi/scrollbar-width/compare/v1.6.1...v1.7.0) (2020-01-27)


### Features

* add weekly downloads badge; ([59647de](https://github.com/xobotyi/scrollbar-width/commit/59647de964f6f87936d61d7f876f518c8b1ff66a))
* add weekly downloads badge; ([da4a506](https://github.com/xobotyi/scrollbar-width/commit/da4a5063c02c93a72580627b843967071d2b185f))

## [1.6.1](https://github.com/xobotyi/scrollbar-width/compare/v1.6.0...v1.6.1) (2020-01-27)


### Bug Fixes

* add root files to the linting process; ([48c7e3c](https://github.com/xobotyi/scrollbar-width/commit/48c7e3ce24f4adce1bc6411fece4084d7a0ead3f))

# [1.6.0](https://github.com/xobotyi/scrollbar-width/compare/v1.5.0...v1.6.0) (2020-01-27)


### Bug Fixes

* TS config now does not conflicts with rollup ([564d523](https://github.com/xobotyi/scrollbar-width/commit/564d523f3f9b5d578cfcbc15b966b59dbda2dd0c))
* tweak eslint config and fix the code; ([456d726](https://github.com/xobotyi/scrollbar-width/commit/456d72636ded5833a18ab7d5d20e56c9dea120ef))


### Features

* get rid of .lock file - not needed here. ([d5415e2](https://github.com/xobotyi/scrollbar-width/commit/d5415e2053b3f2920891379e318a6654ed7171c3))
* Replace tslint with eslint ([e4f29a2](https://github.com/xobotyi/scrollbar-width/commit/e4f29a2f79b5c0d673c0cfdf7727915d2604c940))

# [1.5.0](https://github.com/xobotyi/scrollbar-width/compare/v1.4.1...v1.5.0) (2019-12-09)


### Features

* Add extra check of DOM readiness. ([0bf89ef](https://github.com/xobotyi/scrollbar-width/commit/0bf89efbf1344c772f5766f14fc60722c9c077a8))

## [1.4.1](https://github.com/xobotyi/scrollbar-width/compare/v1.4.0...v1.4.1) (2019-11-19)


### Bug Fixes

* **coverage:** Fix codacy coverage push; ([b1545af](https://github.com/xobotyi/scrollbar-width/commit/b1545afa2c4c3c6d15fdd6404af5aa9e4d59f649))

# [1.4.0](https://github.com/xobotyi/scrollbar-width/compare/v1.3.0...v1.4.0) (2019-11-19)


### Bug Fixes

* **tests:** Linux detection fix; ([be38927](https://github.com/xobotyi/scrollbar-width/commit/be389270341acd650bd1bd81885dfd027b0626fe))
* FireFox on linux have SBW of width 16 ([8d10f9b](https://github.com/xobotyi/scrollbar-width/commit/8d10f9b0fd2c1ce198484dbc94a27d0521e19aa2))


### Features

* modify travis config to push coverage and release the version only if everything is successful. ([5deec11](https://github.com/xobotyi/scrollbar-width/commit/5deec119ee9eaf771a7cc24c94566b2bbc0d8fdf))

# [1.3.0](https://github.com/xobotyi/scrollbar-width/compare/v1.2.4...v1.3.0) (2019-11-19)


### Bug Fixes

* reconfigure travis to perform tests and push coverage to the codacy; ([cfc5c76](https://github.com/xobotyi/scrollbar-width/commit/cfc5c760a2390aede38f1231e188ec3dfea457ad))


### Features

* add tests ([7d920cc](https://github.com/xobotyi/scrollbar-width/commit/7d920cc621cea0124561afa6d25c8a2a398ff646))
* Added codacy badges (coverage and grade) ([aa723f7](https://github.com/xobotyi/scrollbar-width/commit/aa723f7a58a04f3b067516f27397489993f1ecd3))

## [1.2.4](https://github.com/xobotyi/scrollbar-width/compare/v1.2.3...v1.2.4) (2019-11-19)


### Bug Fixes

* newline after NOTE: in README.md ([a2cfc39](https://github.com/xobotyi/scrollbar-width/commit/a2cfc39bde150e89867462b6c74c9e0b74dc8a93))

## [1.2.3](https://github.com/xobotyi/scrollbar-width/compare/v1.2.2...v1.2.3) (2019-11-19)


### Bug Fixes

* newline after NOTE: in README.md ([646153c](https://github.com/xobotyi/scrollbar-width/commit/646153cc3e4fb43122b629739d1ebcd98b0573cf))

## [1.2.2](https://github.com/xobotyi/scrollbar-width/compare/v1.2.1...v1.2.2) (2019-11-19)


### Bug Fixes

* shorten the description; ([dc6945a](https://github.com/xobotyi/scrollbar-width/commit/dc6945a717cd6ea22bc1e877a9571d387ea0f4b8))

## [1.2.1](https://github.com/xobotyi/scrollbar-width/compare/v1.2.0...v1.2.1) (2019-11-19)


### Bug Fixes

* Mention that in SSR environment it will return 0; ([0562ef8](https://github.com/xobotyi/scrollbar-width/commit/0562ef809177896b6db8b16f3bfadd4b6b2962a9))
* Strictly require `true` to recalculate the width; ([d25db0b](https://github.com/xobotyi/scrollbar-width/commit/d25db0b968bce67fe5b27bd92e65c1d71c99ea9f))

# [1.2.0](https://github.com/xobotyi/scrollbar-width/compare/v1.1.1...v1.2.0) (2019-11-19)


### Features

* Add `access: public` in package.json; ([e373d9e](https://github.com/xobotyi/scrollbar-width/commit/e373d9e582b78b2b01f67adb3c1feccb7f573c85))

## [1.1.1](https://github.com/xobotyi/scrollbar-width/compare/v1.1.0...v1.1.1) (2019-11-19)


### Bug Fixes

* add github plugin to verify stage ([55eb00d](https://github.com/xobotyi/scrollbar-width/commit/55eb00d557565ac891c99c486abe4c4ccb6e6d5a))

# [1.1.0](https://github.com/xobotyi/scrollbar-width/compare/v1.0.0...v1.1.0) (2019-11-18)


### Bug Fixes

* remove npm cache from travis config; ([1fab1e9](https://github.com/xobotyi/scrollbar-width/commit/1fab1e9c986206751c2275ce93cd7a89c8a80a3d))


### Features

* Live example version added to README.md; ([d571839](https://github.com/xobotyi/scrollbar-width/commit/d5718394cc51a5d68cec2d07c68624d0f3c8e897))
* Readme's header now centered; ([f9761d6](https://github.com/xobotyi/scrollbar-width/commit/f9761d6e772c22c5bfcd5e1a9eb92d2e569a456c))

# 1.0.0 (2019-11-18)


### Bug Fixes

* try to fix semantic release error; ([bce4aa5](https://github.com/xobotyi/scrollbar-width/commit/bce4aa550a69502cfa6cb164500bb904ed3680ec))
* umd version now exports function as xobotyi.scrollbarWidth; ([f5ea7b1](https://github.com/xobotyi/scrollbar-width/commit/f5ea7b1842f48bfd1503df2cd33f857a54815bd6))


### Features

* Readme; ([ab535a4](https://github.com/xobotyi/scrollbar-width/commit/ab535a4213e93ae841e6b041eece1a3251037dc2))
