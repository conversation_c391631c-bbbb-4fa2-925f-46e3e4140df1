{"name": "rtl-css-js", "version": "1.16.1", "description": "Right To Left conversion for CSS in JS objects", "main": "dist/cjs/index.js", "jsnext:main": "dist/esm/index.js", "module": "dist/esm/index.js", "types": "types.d.ts", "exports": {".": {"types": "./types.d.ts", "node": "./dist/cjs/index.js", "import": "./dist/esm/index.js", "default": "./dist/cjs/index.js"}, "./core": {"types": "./core.d.ts", "node": "./dist/cjs/core.js", "import": "./dist/esm/core.js", "default": "./dist/cjs/core.js"}, "./core.esm": {"default": "./dist/esm/core.js"}, "./core.esm.js": {"default": "./dist/esm/core.js"}, "./core.js": {"default": "./dist/cjs/core.js"}, "./package.json": "./package.json"}, "scripts": {"build": "rimraf dist && npm-run-all build:**", "build:bundlers": "kcd-scripts build --bundle cjs,esm --environment BUILD_INPUT:src/*.js --no-clean", "build:umd:main": "kcd-scripts build --bundle umd,umd.min BUILD_NAME:rtlCSSJS --no-clean", "build:umd:core": "kcd-scripts build --bundle umd,umd.min --environment BUILD_NAME:rtlCSSJSCore,BUILD_FILENAME_SUFFIX:\".core\",BUILD_INPUT:src/core.js --no-clean", "postbuild:bundlers": "node scripts/add-proxies-typings.js", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm run test -s -- --coverage --updateSnapshot", "validate": "kcd-scripts validate"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "files": ["dist", "core", "types.d.ts", "core.d.ts", "core.esm.js", "core.esm.d.ts"], "keywords": ["css-in-js", "ltr", "rtl", "c<PERSON><PERSON><PERSON>"], "author": "<PERSON> <PERSON> <<EMAIL>> (http://kentcdodds.com/)", "license": "MIT", "devDependencies": {"kcd-scripts": "^1.11.0", "npm-run-all": "^4.1.1", "rimraf": "^3.0.0", "tiny-glob": "^0.2.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "repository": {"type": "git", "url": "https://github.com/kentcdodds/rtl-css-js.git"}, "bugs": {"url": "https://github.com/kentcdodds/rtl-css-js/issues"}, "homepage": "https://github.com/kentcdodds/rtl-css-js#readme", "dependencies": {"@babel/runtime": "^7.1.2"}}