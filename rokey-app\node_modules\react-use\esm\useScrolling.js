import { useEffect, useState } from 'react';
import { off, on } from './misc/util';
var useScrolling = function (ref) {
    var _a = useState(false), scrolling = _a[0], setScrolling = _a[1];
    useEffect(function () {
        if (ref.current) {
            var scrollingTimeout_1;
            var handleScrollEnd_1 = function () {
                setScrolling(false);
            };
            var handleScroll_1 = function () {
                setScrolling(true);
                clearTimeout(scrollingTimeout_1);
                scrollingTimeout_1 = setTimeout(function () { return handleScrollEnd_1(); }, 150);
            };
            on(ref.current, 'scroll', handleScroll_1, false);
            return function () {
                if (ref.current) {
                    off(ref.current, 'scroll', handleScroll_1, false);
                }
            };
        }
        return function () { };
    }, [ref]);
    return scrolling;
};
export default useScrolling;
