import { useEffect, useState } from 'react';
import { isBrowser } from './misc/util';
var getInitialState = function (query, defaultState) {
    // Prevent a React hydration mismatch when a default value is provided by not defaulting to window.matchMedia(query).matches.
    if (defaultState !== undefined) {
        return defaultState;
    }
    if (isBrowser) {
        return window.matchMedia(query).matches;
    }
    // A default value has not been provided, and you are rendering on the server, warn of a possible hydration mismatch when defaulting to false.
    if (process.env.NODE_ENV !== 'production') {
        console.warn('`useMedia` When server side rendering, defaultState should be defined to prevent a hydration mismatches.');
    }
    return false;
};
var useMedia = function (query, defaultState) {
    var _a = useState(getInitialState(query, defaultState)), state = _a[0], setState = _a[1];
    useEffect(function () {
        var mounted = true;
        var mql = window.matchMedia(query);
        var onChange = function () {
            if (!mounted) {
                return;
            }
            setState(!!mql.matches);
        };
        mql.addEventListener('change', onChange);
        setState(mql.matches);
        return function () {
            mounted = false;
            mql.removeEventListener('change', onChange);
        };
    }, [query]);
    return state;
};
export default useMedia;
