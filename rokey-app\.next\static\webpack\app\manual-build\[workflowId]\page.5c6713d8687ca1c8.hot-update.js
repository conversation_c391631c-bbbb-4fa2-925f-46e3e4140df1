"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/BaseNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction BaseNode(param) {\n    let { data, children, icon: Icon, color = '#ff6b35', hasInput = true, hasOutput = true, inputLabel = 'Input', outputLabel = 'Output', className = '' } = param;\n    const isConfigured = data.isConfigured;\n    const hasError = data.hasError;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            hasInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"target\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Left,\n                className: \"w-4 h-4 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors\",\n                style: {\n                    left: -8\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-w-[200px] rounded-lg border-2 transition-all duration-200 \".concat(hasError ? 'border-red-500 bg-red-900/20' : isConfigured ? 'border-gray-600 bg-gray-800/90' : 'border-yellow-500 bg-yellow-900/20', \" backdrop-blur-sm shadow-lg hover:shadow-xl\"),\n                style: {\n                    borderColor: hasError ? '#ef4444' : isConfigured ? color : '#eab308'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 rounded-t-lg flex items-center gap-3\",\n                        style: {\n                            background: hasError ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))' : \"linear-gradient(135deg, \".concat(color, \"20, \").concat(color, \"10)\")\n                        },\n                        children: [\n                            Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg\",\n                                style: {\n                                    backgroundColor: hasError ? '#ef444420' : \"\".concat(color, \"20\"),\n                                    color: hasError ? '#ef4444' : color\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium text-white text-sm\",\n                                        children: data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this),\n                                    data.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: data.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\",\n                                    title: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this) : isConfigured ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\",\n                                    title: \"Configured\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\",\n                                    title: \"Needs configuration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-t border-gray-700/50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this),\n                    hasError && data.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-red-300\",\n                            children: data.errorMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            hasOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_1__.Handle, {\n                type: \"source\",\n                position: _xyflow_react__WEBPACK_IMPORTED_MODULE_2__.Position.Right,\n                className: \"w-3 h-3 border-2 border-gray-600 bg-gray-800\",\n                style: {\n                    right: -6\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\BaseNode.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_c = BaseNode;\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\n"));

/***/ })

});