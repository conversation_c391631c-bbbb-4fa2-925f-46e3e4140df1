"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/manual-build/[workflowId]/page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkflowEditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react_dist_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react/dist/style.css */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/style.css\");\n/* harmony import */ var _components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/manual-build/WorkflowToolbar */ \"(app-pages-browser)/./src/components/manual-build/WorkflowToolbar.tsx\");\n/* harmony import */ var _components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/manual-build/NodePalette */ \"(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\");\n/* harmony import */ var _components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/manual-build/NodeConfigPanel */ \"(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\");\n/* harmony import */ var _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/manual-build/nodes */ \"(app-pages-browser)/./src/components/manual-build/nodes/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction WorkflowEditorPage(param) {\n    let { params } = param;\n    _s();\n    const resolvedParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const workflowId = resolvedParams === null || resolvedParams === void 0 ? void 0 : resolvedParams.workflowId;\n    const [workflow, setWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [nodes, setNodes, onNodesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.useNodesState)([]);\n    const [edges, setEdges, onEdgesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.useEdgesState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDirty, setIsDirty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load workflow data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkflowEditorPage.useEffect\": ()=>{\n            if (workflowId === 'new') {\n                initializeNewWorkflow();\n            } else {\n                loadWorkflow(workflowId);\n            }\n        }\n    }[\"WorkflowEditorPage.useEffect\"], [\n        workflowId\n    ]);\n    const initializeNewWorkflow = async ()=>{\n        try {\n            // Create default nodes for new workflow\n            const defaultNodes = [\n                {\n                    id: 'user-request',\n                    type: 'userRequest',\n                    position: {\n                        x: 50,\n                        y: 200\n                    },\n                    data: {\n                        label: 'User Request',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Starting point for user input'\n                    }\n                },\n                {\n                    id: 'classifier',\n                    type: 'classifier',\n                    position: {\n                        x: 350,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Classifier',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Analyzes and categorizes the request'\n                    }\n                },\n                {\n                    id: 'output',\n                    type: 'output',\n                    position: {\n                        x: 950,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Output',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Final response to the user'\n                    }\n                }\n            ];\n            const defaultEdges = [\n                {\n                    id: 'e1',\n                    source: 'user-request',\n                    target: 'classifier',\n                    type: 'smoothstep',\n                    animated: true\n                }\n            ];\n            setNodes(defaultNodes);\n            setEdges(defaultEdges);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to initialize new workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const loadWorkflow = async (id)=>{\n        try {\n            // TODO: Implement API call to load workflow\n            console.log('Loading workflow:', id);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to load workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const onConnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onConnect]\": (params)=>{\n            const newEdge = {\n                ...params,\n                id: \"e\".concat(edges.length + 1),\n                type: 'smoothstep',\n                animated: true\n            };\n            setEdges({\n                \"WorkflowEditorPage.useCallback[onConnect]\": (eds)=>(0,_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.addEdge)(newEdge, eds)\n            }[\"WorkflowEditorPage.useCallback[onConnect]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[onConnect]\"], [\n        edges.length,\n        setEdges\n    ]);\n    const onNodeClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeClick]\": (event, node)=>{\n            setSelectedNode(node);\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeClick]\"], []);\n    const onPaneClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onPaneClick]\": ()=>{\n            setSelectedNode(null);\n        }\n    }[\"WorkflowEditorPage.useCallback[onPaneClick]\"], []);\n    const handleSave = async ()=>{\n        if (!workflow && workflowId === 'new') {\n            // Show save dialog for new workflow\n            const name = prompt('Enter workflow name:');\n            if (!name) return;\n            // TODO: Implement save new workflow\n            console.log('Saving new workflow:', name);\n        } else {\n            // Update existing workflow\n            setIsSaving(true);\n            try {\n                // TODO: Implement update workflow API call\n                console.log('Updating workflow:', workflowId);\n                setIsDirty(false);\n            } catch (error) {\n                console.error('Failed to save workflow:', error);\n            } finally{\n                setIsSaving(false);\n            }\n        }\n    };\n    const handleExecute = async ()=>{\n        // TODO: Implement workflow execution\n        console.log('Executing workflow');\n    };\n    const handleAddNode = (nodeType, position)=>{\n        const newNode = {\n            id: \"\".concat(nodeType, \"-\").concat(Date.now()),\n            type: nodeType,\n            position,\n            data: {\n                label: nodeType.charAt(0).toUpperCase() + nodeType.slice(1),\n                config: {},\n                isConfigured: false,\n                description: \"\".concat(nodeType, \" node\")\n            }\n        };\n        setNodes((nds)=>[\n                ...nds,\n                newNode\n            ]);\n        setIsDirty(true);\n    };\n    const handleNodeUpdate = (nodeId, updates)=>{\n        setNodes((nds)=>nds.map((node)=>node.id === nodeId ? {\n                    ...node,\n                    data: {\n                        ...node.data,\n                        ...updates\n                    }\n                } : node));\n        setIsDirty(true);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen bg-[#040716] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"Loading workflow...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#040716] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                workflow: workflow,\n                isDirty: isDirty,\n                isSaving: isSaving,\n                onSave: handleSave,\n                onExecute: handleExecute,\n                onBack: ()=>router.push('/manual-build')\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onAddNode: handleAddNode\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.ReactFlow, {\n                            nodes: nodes,\n                            edges: edges,\n                            onNodesChange: onNodesChange,\n                            onEdgesChange: onEdgesChange,\n                            onConnect: onConnect,\n                            onNodeClick: onNodeClick,\n                            onPaneClick: onPaneClick,\n                            nodeTypes: _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_7__.nodeTypes,\n                            fitView: true,\n                            className: \"bg-[#040716]\",\n                            defaultViewport: {\n                                x: 0,\n                                y: 0,\n                                zoom: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.Background, {\n                                    color: \"#1f2937\",\n                                    gap: 20,\n                                    size: 1,\n                                    variant: \"dots\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.Controls, {\n                                    className: \"bg-gray-800 border border-gray-700\",\n                                    showInteractive: false\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.MiniMap, {\n                                    className: \"bg-gray-800 border border-gray-700\",\n                                    nodeColor: \"#ff6b35\",\n                                    maskColor: \"rgba(0, 0, 0, 0.2)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        node: selectedNode,\n                        onUpdate: (updates)=>handleNodeUpdate(selectedNode.id, updates),\n                        onClose: ()=>setSelectedNode(null)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_s(WorkflowEditorPage, \"+ha6TECq9QWrSrdyjgU3E/oT4Y4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_8__.useNodesState,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_8__.useEdgesState\n    ];\n});\n_c = WorkflowEditorPage;\nvar _c;\n$RefreshReg$(_c, \"WorkflowEditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx\n"));

/***/ })

});