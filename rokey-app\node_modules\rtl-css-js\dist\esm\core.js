export { f as arrayToObject, h as calculateNewBackgroundPosition, j as calculateNewTranslate, i as canConvertValue, c as convert, d as convertProperty, k as flipSign, j as flipTransformSign, g as getPropertyDoppelganger, e as getValueDoppelganger, u as getValuesAsList, l as handleQuartetValues, m as includes, n as isBoolean, o as isFunction, r as isNullOrUndefined, q as isNumber, s as isObject, t as isString, a as propertiesToConvert, p as propertyValueConverters, b as propsToIgnore, w as splitShadow, v as valuesToConvert } from './convert-9768a965.js';
