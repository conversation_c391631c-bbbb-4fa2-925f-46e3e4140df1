"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CloudArrowDownIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z\"\n    }));\n}\n_c = CloudArrowDownIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CloudArrowDownIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CloudArrowDownIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx":
/*!*********************************************************!*\
  !*** ./src/components/manual-build/NodeConfigPanel.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodeConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowDownIcon,Cog6ToothIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction NodeConfigPanel(param) {\n    let { node, onUpdate, onClose } = param;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.data.config);\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch models from database\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                console.error('Error fetching models:', err);\n                setFetchProviderModelsError(err.message);\n                setFetchedProviderModels([]);\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"NodeConfigPanel.useCallback[fetchModelsFromDatabase]\"], []);\n    // Load models on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NodeConfigPanel.useEffect\": ()=>{\n            if (node.type === 'provider') {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"NodeConfigPanel.useEffect\"], [\n        node.type,\n        fetchModelsFromDatabase\n    ]);\n    const handleConfigChange = (key, value)=>{\n        const newConfig = {\n            ...config,\n            [key]: value\n        };\n        setConfig(newConfig);\n        onUpdate({\n            config: newConfig,\n            isConfigured: isNodeConfigured(node.type, newConfig)\n        });\n    };\n    // Model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NodeConfigPanel.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels && node.type === 'provider') {\n                const providerConfig = config;\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_2__.llmProviders.find({\n                    \"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === providerConfig.providerId\n                }[\"NodeConfigPanel.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"NodeConfigPanel.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).map({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]).sort({\n                    \"NodeConfigPanel.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"NodeConfigPanel.useMemo[modelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"NodeConfigPanel.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        config,\n        node.type\n    ]);\n    const isNodeConfigured = (nodeType, nodeConfig)=>{\n        switch(nodeType){\n            case 'provider':\n                return !!(nodeConfig.providerId && nodeConfig.modelId);\n            case 'roleAgent':\n                return !!(nodeConfig.roleId && nodeConfig.roleName);\n            case 'conditional':\n                return !!(nodeConfig.condition && nodeConfig.conditionType);\n            case 'tool':\n                return !!nodeConfig.toolType;\n            case 'memory':\n                return !!(nodeConfig.memoryType && nodeConfig.storageKey);\n            case 'switch':\n                var _nodeConfig_cases;\n                return !!(nodeConfig.switchType && ((_nodeConfig_cases = nodeConfig.cases) === null || _nodeConfig_cases === void 0 ? void 0 : _nodeConfig_cases.length) > 0);\n            case 'loop':\n                return !!nodeConfig.loopType;\n            default:\n                return true;\n        }\n    };\n    const renderProviderConfig = ()=>{\n        var _providerConfig_parameters, _providerConfig_parameters1;\n        const providerConfig = config;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: providerConfig.providerId || '',\n                            onChange: (e)=>{\n                                handleConfigChange('providerId', e.target.value);\n                                // Reset model when provider changes\n                                handleConfigChange('modelId', '');\n                            },\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option.value,\n                                        children: option.label\n                                    }, option.value, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: providerConfig.apiKey || '',\n                            onChange: (e)=>handleConfigChange('apiKey', e.target.value),\n                            placeholder: \"Enter your API key\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                \"Fetching models...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg\",\n                            children: [\n                                \"Error: \",\n                                fetchProviderModelsError\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                providerConfig.providerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Model\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: providerConfig.modelId || '',\n                            onChange: (e)=>handleConfigChange('modelId', e.target.value),\n                            disabled: !modelOptions.length,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30\",\n                            children: modelOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select Model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this),\n                                    modelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: option.value,\n                                            children: option.label\n                                        }, option.value, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 21\n                                        }, this))\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                disabled: true,\n                                children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Temperature\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"0\",\n                                    max: \"2\",\n                                    step: \"0.1\",\n                                    value: ((_providerConfig_parameters = providerConfig.parameters) === null || _providerConfig_parameters === void 0 ? void 0 : _providerConfig_parameters.temperature) || 1.0,\n                                    onChange: (e)=>handleConfigChange('parameters', {\n                                            ...providerConfig.parameters,\n                                            temperature: parseFloat(e.target.value)\n                                        }),\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"Max Tokens\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"1\",\n                                    value: ((_providerConfig_parameters1 = providerConfig.parameters) === null || _providerConfig_parameters1 === void 0 ? void 0 : _providerConfig_parameters1.maxTokens) || '',\n                                    onChange: (e)=>handleConfigChange('parameters', {\n                                            ...providerConfig.parameters,\n                                            maxTokens: parseInt(e.target.value) || undefined\n                                        }),\n                                    placeholder: \"Auto\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this),\n                providerConfig.providerId === 'openrouter' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-blue-300 font-medium mb-1\",\n                            children: \"\\uD83C\\uDF10 OpenRouter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-200\",\n                            children: \"Access to 300+ models from multiple providers with a single API key.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    };\n    const renderRoleAgentConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Role Name\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.roleName || '',\n                            onChange: (e)=>handleConfigChange('roleName', e.target.value),\n                            placeholder: \"e.g., Coder, Writer, Analyst\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Custom Prompt\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: config.customPrompt || '',\n                            onChange: (e)=>handleConfigChange('customPrompt', e.target.value),\n                            placeholder: \"Enter custom instructions for this role...\",\n                            rows: 4,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: config.memoryEnabled || false,\n                                onChange: (e)=>handleConfigChange('memoryEnabled', e.target.checked),\n                                className: \"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-300\",\n                                children: \"Enable memory\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConditionalConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition Type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: config.conditionType || '',\n                            onChange: (e)=>handleConfigChange('conditionType', e.target.value),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"contains\",\n                                    children: \"Contains\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"equals\",\n                                    children: \"Equals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"regex\",\n                                    children: \"Regex\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"length\",\n                                    children: \"Length\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"custom\",\n                                    children: \"Custom\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Condition\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: config.condition || '',\n                            onChange: (e)=>handleConfigChange('condition', e.target.value),\n                            placeholder: \"Enter condition...\",\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"True Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.trueLabel || '',\n                                    onChange: (e)=>handleConfigChange('trueLabel', e.target.value),\n                                    placeholder: \"Continue\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                    children: \"False Label\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: config.falseLabel || '',\n                                    onChange: (e)=>handleConfigChange('falseLabel', e.target.value),\n                                    placeholder: \"Skip\",\n                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 319,\n            columnNumber: 7\n        }, this);\n    };\n    const renderDefaultConfig = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Label\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: node.data.label,\n                            onChange: (e)=>onUpdate({\n                                    label: e.target.value\n                                }),\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                            children: \"Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: node.data.description || '',\n                            onChange: (e)=>onUpdate({\n                                    description: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n            lineNumber: 383,\n            columnNumber: 7\n        }, this);\n    };\n    const renderConfigContent = ()=>{\n        switch(node.type){\n            case 'provider':\n                return renderProviderConfig();\n            case 'roleAgent':\n                return renderRoleAgentConfig();\n            case 'conditional':\n                return renderConditionalConfig();\n            default:\n                return renderDefaultConfig();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-[#ff6b35]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Configure Node\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: node.data.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-400 hover:text-white transition-colors p-1 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowDownIcon_Cog6ToothIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: renderConfigContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 rounded-lg border border-gray-700/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full \".concat(node.data.isConfigured ? 'bg-green-500' : 'bg-yellow-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: node.data.isConfigured ? 'Configured' : 'Needs Configuration'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400\",\n                        children: node.data.isConfigured ? 'This node is properly configured and ready to use.' : 'Complete the configuration to use this node in your workflow.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n                lineNumber: 455,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodeConfigPanel.tsx\",\n        lineNumber: 425,\n        columnNumber: 5\n    }, this);\n}\n_s(NodeConfigPanel, \"hoRKQcjeYT6L8h8tdKK/ym4mAP4=\");\n_c2 = NodeConfigPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"NodeConfigPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\n"));

/***/ })

});