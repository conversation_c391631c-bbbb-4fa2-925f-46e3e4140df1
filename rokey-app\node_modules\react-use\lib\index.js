"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useLogger = exports.useLockBodyScroll = exports.useLocation = exports.useLocalStorage = exports.useList = exports.useLifecycles = exports.useLatest = exports.useKeyPressEvent = exports.useKeyPress = exports.createBreakpoint = exports.useKey = exports.useIsomorphicLayoutEffect = exports.useInterval = exports.useIntersection = exports.useIdle = exports.useHoverDirty = exports.useHover = exports.useHarmonicIntervalFn = exports.useGetSetState = exports.useGetSet = exports.useGeolocation = exports.useFullscreen = exports.useFavicon = exports.useError = exports.useEvent = exports.ensuredForwardRef = exports.useEnsuredForwardedRef = exports.useEffectOnce = exports.useDropArea = exports.useDrop = exports.useDefault = exports.useDeepCompareEffect = exports.useDebounce = exports.useCustomCompareEffect = exports.useCss = exports.useCounter = exports.useCopyToClipboard = exports.useCookie = exports.useClickAway = exports.useBoolean = exports.useBeforeUnload = exports.useBattery = exports.useAudio = exports.useAsyncRetry = exports.useAsyncFn = exports.useAsync = exports.createStateContext = exports.createReducer = exports.createReducerContext = exports.createMemo = void 0;
exports.useUpsert = exports.useUpdateEffect = exports.useUpdate = exports.useUnmountPromise = exports.useUnmount = exports.useTween = exports.useToggle = exports.useTitle = exports.useTimeoutFn = exports.useTimeout = exports.useThrottleFn = exports.useThrottle = exports.useStateList = exports.useStateWithHistory = exports.useStartTyping = exports.useSpeech = exports.useSlider = exports.useSize = exports.useShallowCompareEffect = exports.useSetState = exports.useSessionStorage = exports.useScrolling = exports.useScroll = exports.useScratch = exports.useSearchParam = exports.useRafState = exports.useRafLoop = exports.useRaf = exports.useQueue = exports.usePromise = exports.usePreviousDistinct = exports.usePrevious = exports.usePermission = exports.usePageLeave = exports.useOrientation = exports.useObservable = exports.useNumber = exports.useNetworkState = exports.useMouseWheel = exports.useMouseHovered = exports.useMouse = exports.useMountedState = exports.useMount = exports.useMotion = exports.useMethods = exports.useMediatedState = exports.useMediaDevices = exports.useMedia = exports.useMap = exports.useLongPress = void 0;
exports.useHash = exports.createGlobalState = exports.useSet = exports.useFirstMountState = exports.useRendersCount = exports.usePinchZoom = exports.useMeasure = exports.useWindowSize = exports.useWindowScroll = exports.useMultiStateValidator = exports.useScrollbarWidth = exports.useStateValidator = exports.useVideo = exports.useVibrate = void 0;
var createMemo_1 = require("./factory/createMemo");
Object.defineProperty(exports, "createMemo", { enumerable: true, get: function () { return __importDefault(createMemo_1).default; } });
var createReducerContext_1 = require("./factory/createReducerContext");
Object.defineProperty(exports, "createReducerContext", { enumerable: true, get: function () { return __importDefault(createReducerContext_1).default; } });
var createReducer_1 = require("./factory/createReducer");
Object.defineProperty(exports, "createReducer", { enumerable: true, get: function () { return __importDefault(createReducer_1).default; } });
var createStateContext_1 = require("./factory/createStateContext");
Object.defineProperty(exports, "createStateContext", { enumerable: true, get: function () { return __importDefault(createStateContext_1).default; } });
var useAsync_1 = require("./useAsync");
Object.defineProperty(exports, "useAsync", { enumerable: true, get: function () { return __importDefault(useAsync_1).default; } });
var useAsyncFn_1 = require("./useAsyncFn");
Object.defineProperty(exports, "useAsyncFn", { enumerable: true, get: function () { return __importDefault(useAsyncFn_1).default; } });
var useAsyncRetry_1 = require("./useAsyncRetry");
Object.defineProperty(exports, "useAsyncRetry", { enumerable: true, get: function () { return __importDefault(useAsyncRetry_1).default; } });
var useAudio_1 = require("./useAudio");
Object.defineProperty(exports, "useAudio", { enumerable: true, get: function () { return __importDefault(useAudio_1).default; } });
var useBattery_1 = require("./useBattery");
Object.defineProperty(exports, "useBattery", { enumerable: true, get: function () { return __importDefault(useBattery_1).default; } });
var useBeforeUnload_1 = require("./useBeforeUnload");
Object.defineProperty(exports, "useBeforeUnload", { enumerable: true, get: function () { return __importDefault(useBeforeUnload_1).default; } });
var useBoolean_1 = require("./useBoolean");
Object.defineProperty(exports, "useBoolean", { enumerable: true, get: function () { return __importDefault(useBoolean_1).default; } });
var useClickAway_1 = require("./useClickAway");
Object.defineProperty(exports, "useClickAway", { enumerable: true, get: function () { return __importDefault(useClickAway_1).default; } });
var useCookie_1 = require("./useCookie");
Object.defineProperty(exports, "useCookie", { enumerable: true, get: function () { return __importDefault(useCookie_1).default; } });
var useCopyToClipboard_1 = require("./useCopyToClipboard");
Object.defineProperty(exports, "useCopyToClipboard", { enumerable: true, get: function () { return __importDefault(useCopyToClipboard_1).default; } });
var useCounter_1 = require("./useCounter");
Object.defineProperty(exports, "useCounter", { enumerable: true, get: function () { return __importDefault(useCounter_1).default; } });
var useCss_1 = require("./useCss");
Object.defineProperty(exports, "useCss", { enumerable: true, get: function () { return __importDefault(useCss_1).default; } });
var useCustomCompareEffect_1 = require("./useCustomCompareEffect");
Object.defineProperty(exports, "useCustomCompareEffect", { enumerable: true, get: function () { return __importDefault(useCustomCompareEffect_1).default; } });
var useDebounce_1 = require("./useDebounce");
Object.defineProperty(exports, "useDebounce", { enumerable: true, get: function () { return __importDefault(useDebounce_1).default; } });
var useDeepCompareEffect_1 = require("./useDeepCompareEffect");
Object.defineProperty(exports, "useDeepCompareEffect", { enumerable: true, get: function () { return __importDefault(useDeepCompareEffect_1).default; } });
var useDefault_1 = require("./useDefault");
Object.defineProperty(exports, "useDefault", { enumerable: true, get: function () { return __importDefault(useDefault_1).default; } });
var useDrop_1 = require("./useDrop");
Object.defineProperty(exports, "useDrop", { enumerable: true, get: function () { return __importDefault(useDrop_1).default; } });
var useDropArea_1 = require("./useDropArea");
Object.defineProperty(exports, "useDropArea", { enumerable: true, get: function () { return __importDefault(useDropArea_1).default; } });
var useEffectOnce_1 = require("./useEffectOnce");
Object.defineProperty(exports, "useEffectOnce", { enumerable: true, get: function () { return __importDefault(useEffectOnce_1).default; } });
var useEnsuredForwardedRef_1 = require("./useEnsuredForwardedRef");
Object.defineProperty(exports, "useEnsuredForwardedRef", { enumerable: true, get: function () { return __importDefault(useEnsuredForwardedRef_1).default; } });
Object.defineProperty(exports, "ensuredForwardRef", { enumerable: true, get: function () { return useEnsuredForwardedRef_1.ensuredForwardRef; } });
var useEvent_1 = require("./useEvent");
Object.defineProperty(exports, "useEvent", { enumerable: true, get: function () { return __importDefault(useEvent_1).default; } });
var useError_1 = require("./useError");
Object.defineProperty(exports, "useError", { enumerable: true, get: function () { return __importDefault(useError_1).default; } });
var useFavicon_1 = require("./useFavicon");
Object.defineProperty(exports, "useFavicon", { enumerable: true, get: function () { return __importDefault(useFavicon_1).default; } });
var useFullscreen_1 = require("./useFullscreen");
Object.defineProperty(exports, "useFullscreen", { enumerable: true, get: function () { return __importDefault(useFullscreen_1).default; } });
var useGeolocation_1 = require("./useGeolocation");
Object.defineProperty(exports, "useGeolocation", { enumerable: true, get: function () { return __importDefault(useGeolocation_1).default; } });
var useGetSet_1 = require("./useGetSet");
Object.defineProperty(exports, "useGetSet", { enumerable: true, get: function () { return __importDefault(useGetSet_1).default; } });
var useGetSetState_1 = require("./useGetSetState");
Object.defineProperty(exports, "useGetSetState", { enumerable: true, get: function () { return __importDefault(useGetSetState_1).default; } });
var useHarmonicIntervalFn_1 = require("./useHarmonicIntervalFn");
Object.defineProperty(exports, "useHarmonicIntervalFn", { enumerable: true, get: function () { return __importDefault(useHarmonicIntervalFn_1).default; } });
var useHover_1 = require("./useHover");
Object.defineProperty(exports, "useHover", { enumerable: true, get: function () { return __importDefault(useHover_1).default; } });
var useHoverDirty_1 = require("./useHoverDirty");
Object.defineProperty(exports, "useHoverDirty", { enumerable: true, get: function () { return __importDefault(useHoverDirty_1).default; } });
var useIdle_1 = require("./useIdle");
Object.defineProperty(exports, "useIdle", { enumerable: true, get: function () { return __importDefault(useIdle_1).default; } });
var useIntersection_1 = require("./useIntersection");
Object.defineProperty(exports, "useIntersection", { enumerable: true, get: function () { return __importDefault(useIntersection_1).default; } });
var useInterval_1 = require("./useInterval");
Object.defineProperty(exports, "useInterval", { enumerable: true, get: function () { return __importDefault(useInterval_1).default; } });
var useIsomorphicLayoutEffect_1 = require("./useIsomorphicLayoutEffect");
Object.defineProperty(exports, "useIsomorphicLayoutEffect", { enumerable: true, get: function () { return __importDefault(useIsomorphicLayoutEffect_1).default; } });
var useKey_1 = require("./useKey");
Object.defineProperty(exports, "useKey", { enumerable: true, get: function () { return __importDefault(useKey_1).default; } });
var createBreakpoint_1 = require("./factory/createBreakpoint");
Object.defineProperty(exports, "createBreakpoint", { enumerable: true, get: function () { return __importDefault(createBreakpoint_1).default; } });
// not exported because of peer dependency
// export { default as useKeyboardJs } from './useKeyboardJs';
var useKeyPress_1 = require("./useKeyPress");
Object.defineProperty(exports, "useKeyPress", { enumerable: true, get: function () { return __importDefault(useKeyPress_1).default; } });
var useKeyPressEvent_1 = require("./useKeyPressEvent");
Object.defineProperty(exports, "useKeyPressEvent", { enumerable: true, get: function () { return __importDefault(useKeyPressEvent_1).default; } });
var useLatest_1 = require("./useLatest");
Object.defineProperty(exports, "useLatest", { enumerable: true, get: function () { return __importDefault(useLatest_1).default; } });
var useLifecycles_1 = require("./useLifecycles");
Object.defineProperty(exports, "useLifecycles", { enumerable: true, get: function () { return __importDefault(useLifecycles_1).default; } });
var useList_1 = require("./useList");
Object.defineProperty(exports, "useList", { enumerable: true, get: function () { return __importDefault(useList_1).default; } });
var useLocalStorage_1 = require("./useLocalStorage");
Object.defineProperty(exports, "useLocalStorage", { enumerable: true, get: function () { return __importDefault(useLocalStorage_1).default; } });
var useLocation_1 = require("./useLocation");
Object.defineProperty(exports, "useLocation", { enumerable: true, get: function () { return __importDefault(useLocation_1).default; } });
var useLockBodyScroll_1 = require("./useLockBodyScroll");
Object.defineProperty(exports, "useLockBodyScroll", { enumerable: true, get: function () { return __importDefault(useLockBodyScroll_1).default; } });
var useLogger_1 = require("./useLogger");
Object.defineProperty(exports, "useLogger", { enumerable: true, get: function () { return __importDefault(useLogger_1).default; } });
var useLongPress_1 = require("./useLongPress");
Object.defineProperty(exports, "useLongPress", { enumerable: true, get: function () { return __importDefault(useLongPress_1).default; } });
var useMap_1 = require("./useMap");
Object.defineProperty(exports, "useMap", { enumerable: true, get: function () { return __importDefault(useMap_1).default; } });
var useMedia_1 = require("./useMedia");
Object.defineProperty(exports, "useMedia", { enumerable: true, get: function () { return __importDefault(useMedia_1).default; } });
var useMediaDevices_1 = require("./useMediaDevices");
Object.defineProperty(exports, "useMediaDevices", { enumerable: true, get: function () { return __importDefault(useMediaDevices_1).default; } });
var useMediatedState_1 = require("./useMediatedState");
Object.defineProperty(exports, "useMediatedState", { enumerable: true, get: function () { return useMediatedState_1.useMediatedState; } });
var useMethods_1 = require("./useMethods");
Object.defineProperty(exports, "useMethods", { enumerable: true, get: function () { return __importDefault(useMethods_1).default; } });
var useMotion_1 = require("./useMotion");
Object.defineProperty(exports, "useMotion", { enumerable: true, get: function () { return __importDefault(useMotion_1).default; } });
var useMount_1 = require("./useMount");
Object.defineProperty(exports, "useMount", { enumerable: true, get: function () { return __importDefault(useMount_1).default; } });
var useMountedState_1 = require("./useMountedState");
Object.defineProperty(exports, "useMountedState", { enumerable: true, get: function () { return __importDefault(useMountedState_1).default; } });
var useMouse_1 = require("./useMouse");
Object.defineProperty(exports, "useMouse", { enumerable: true, get: function () { return __importDefault(useMouse_1).default; } });
var useMouseHovered_1 = require("./useMouseHovered");
Object.defineProperty(exports, "useMouseHovered", { enumerable: true, get: function () { return __importDefault(useMouseHovered_1).default; } });
var useMouseWheel_1 = require("./useMouseWheel");
Object.defineProperty(exports, "useMouseWheel", { enumerable: true, get: function () { return __importDefault(useMouseWheel_1).default; } });
var useNetworkState_1 = require("./useNetworkState");
Object.defineProperty(exports, "useNetworkState", { enumerable: true, get: function () { return __importDefault(useNetworkState_1).default; } });
var useNumber_1 = require("./useNumber");
Object.defineProperty(exports, "useNumber", { enumerable: true, get: function () { return __importDefault(useNumber_1).default; } });
var useObservable_1 = require("./useObservable");
Object.defineProperty(exports, "useObservable", { enumerable: true, get: function () { return __importDefault(useObservable_1).default; } });
var useOrientation_1 = require("./useOrientation");
Object.defineProperty(exports, "useOrientation", { enumerable: true, get: function () { return __importDefault(useOrientation_1).default; } });
var usePageLeave_1 = require("./usePageLeave");
Object.defineProperty(exports, "usePageLeave", { enumerable: true, get: function () { return __importDefault(usePageLeave_1).default; } });
var usePermission_1 = require("./usePermission");
Object.defineProperty(exports, "usePermission", { enumerable: true, get: function () { return __importDefault(usePermission_1).default; } });
var usePrevious_1 = require("./usePrevious");
Object.defineProperty(exports, "usePrevious", { enumerable: true, get: function () { return __importDefault(usePrevious_1).default; } });
var usePreviousDistinct_1 = require("./usePreviousDistinct");
Object.defineProperty(exports, "usePreviousDistinct", { enumerable: true, get: function () { return __importDefault(usePreviousDistinct_1).default; } });
var usePromise_1 = require("./usePromise");
Object.defineProperty(exports, "usePromise", { enumerable: true, get: function () { return __importDefault(usePromise_1).default; } });
var useQueue_1 = require("./useQueue");
Object.defineProperty(exports, "useQueue", { enumerable: true, get: function () { return __importDefault(useQueue_1).default; } });
var useRaf_1 = require("./useRaf");
Object.defineProperty(exports, "useRaf", { enumerable: true, get: function () { return __importDefault(useRaf_1).default; } });
var useRafLoop_1 = require("./useRafLoop");
Object.defineProperty(exports, "useRafLoop", { enumerable: true, get: function () { return __importDefault(useRafLoop_1).default; } });
var useRafState_1 = require("./useRafState");
Object.defineProperty(exports, "useRafState", { enumerable: true, get: function () { return __importDefault(useRafState_1).default; } });
var useSearchParam_1 = require("./useSearchParam");
Object.defineProperty(exports, "useSearchParam", { enumerable: true, get: function () { return __importDefault(useSearchParam_1).default; } });
var useScratch_1 = require("./useScratch");
Object.defineProperty(exports, "useScratch", { enumerable: true, get: function () { return __importDefault(useScratch_1).default; } });
var useScroll_1 = require("./useScroll");
Object.defineProperty(exports, "useScroll", { enumerable: true, get: function () { return __importDefault(useScroll_1).default; } });
var useScrolling_1 = require("./useScrolling");
Object.defineProperty(exports, "useScrolling", { enumerable: true, get: function () { return __importDefault(useScrolling_1).default; } });
var useSessionStorage_1 = require("./useSessionStorage");
Object.defineProperty(exports, "useSessionStorage", { enumerable: true, get: function () { return __importDefault(useSessionStorage_1).default; } });
var useSetState_1 = require("./useSetState");
Object.defineProperty(exports, "useSetState", { enumerable: true, get: function () { return __importDefault(useSetState_1).default; } });
var useShallowCompareEffect_1 = require("./useShallowCompareEffect");
Object.defineProperty(exports, "useShallowCompareEffect", { enumerable: true, get: function () { return __importDefault(useShallowCompareEffect_1).default; } });
var useSize_1 = require("./useSize");
Object.defineProperty(exports, "useSize", { enumerable: true, get: function () { return __importDefault(useSize_1).default; } });
var useSlider_1 = require("./useSlider");
Object.defineProperty(exports, "useSlider", { enumerable: true, get: function () { return __importDefault(useSlider_1).default; } });
var useSpeech_1 = require("./useSpeech");
Object.defineProperty(exports, "useSpeech", { enumerable: true, get: function () { return __importDefault(useSpeech_1).default; } });
// not exported because of peer dependency
// export { default as useSpring } from './useSpring';
var useStartTyping_1 = require("./useStartTyping");
Object.defineProperty(exports, "useStartTyping", { enumerable: true, get: function () { return __importDefault(useStartTyping_1).default; } });
var useStateWithHistory_1 = require("./useStateWithHistory");
Object.defineProperty(exports, "useStateWithHistory", { enumerable: true, get: function () { return useStateWithHistory_1.useStateWithHistory; } });
var useStateList_1 = require("./useStateList");
Object.defineProperty(exports, "useStateList", { enumerable: true, get: function () { return __importDefault(useStateList_1).default; } });
var useThrottle_1 = require("./useThrottle");
Object.defineProperty(exports, "useThrottle", { enumerable: true, get: function () { return __importDefault(useThrottle_1).default; } });
var useThrottleFn_1 = require("./useThrottleFn");
Object.defineProperty(exports, "useThrottleFn", { enumerable: true, get: function () { return __importDefault(useThrottleFn_1).default; } });
var useTimeout_1 = require("./useTimeout");
Object.defineProperty(exports, "useTimeout", { enumerable: true, get: function () { return __importDefault(useTimeout_1).default; } });
var useTimeoutFn_1 = require("./useTimeoutFn");
Object.defineProperty(exports, "useTimeoutFn", { enumerable: true, get: function () { return __importDefault(useTimeoutFn_1).default; } });
var useTitle_1 = require("./useTitle");
Object.defineProperty(exports, "useTitle", { enumerable: true, get: function () { return __importDefault(useTitle_1).default; } });
var useToggle_1 = require("./useToggle");
Object.defineProperty(exports, "useToggle", { enumerable: true, get: function () { return __importDefault(useToggle_1).default; } });
var useTween_1 = require("./useTween");
Object.defineProperty(exports, "useTween", { enumerable: true, get: function () { return __importDefault(useTween_1).default; } });
var useUnmount_1 = require("./useUnmount");
Object.defineProperty(exports, "useUnmount", { enumerable: true, get: function () { return __importDefault(useUnmount_1).default; } });
var useUnmountPromise_1 = require("./useUnmountPromise");
Object.defineProperty(exports, "useUnmountPromise", { enumerable: true, get: function () { return __importDefault(useUnmountPromise_1).default; } });
var useUpdate_1 = require("./useUpdate");
Object.defineProperty(exports, "useUpdate", { enumerable: true, get: function () { return __importDefault(useUpdate_1).default; } });
var useUpdateEffect_1 = require("./useUpdateEffect");
Object.defineProperty(exports, "useUpdateEffect", { enumerable: true, get: function () { return __importDefault(useUpdateEffect_1).default; } });
var useUpsert_1 = require("./useUpsert");
Object.defineProperty(exports, "useUpsert", { enumerable: true, get: function () { return __importDefault(useUpsert_1).default; } });
var useVibrate_1 = require("./useVibrate");
Object.defineProperty(exports, "useVibrate", { enumerable: true, get: function () { return __importDefault(useVibrate_1).default; } });
var useVideo_1 = require("./useVideo");
Object.defineProperty(exports, "useVideo", { enumerable: true, get: function () { return __importDefault(useVideo_1).default; } });
var useStateValidator_1 = require("./useStateValidator");
Object.defineProperty(exports, "useStateValidator", { enumerable: true, get: function () { return __importDefault(useStateValidator_1).default; } });
var useScrollbarWidth_1 = require("./useScrollbarWidth");
Object.defineProperty(exports, "useScrollbarWidth", { enumerable: true, get: function () { return useScrollbarWidth_1.useScrollbarWidth; } });
var useMultiStateValidator_1 = require("./useMultiStateValidator");
Object.defineProperty(exports, "useMultiStateValidator", { enumerable: true, get: function () { return useMultiStateValidator_1.useMultiStateValidator; } });
var useWindowScroll_1 = require("./useWindowScroll");
Object.defineProperty(exports, "useWindowScroll", { enumerable: true, get: function () { return __importDefault(useWindowScroll_1).default; } });
var useWindowSize_1 = require("./useWindowSize");
Object.defineProperty(exports, "useWindowSize", { enumerable: true, get: function () { return __importDefault(useWindowSize_1).default; } });
var useMeasure_1 = require("./useMeasure");
Object.defineProperty(exports, "useMeasure", { enumerable: true, get: function () { return __importDefault(useMeasure_1).default; } });
var usePinchZoom_1 = require("./usePinchZoom");
Object.defineProperty(exports, "usePinchZoom", { enumerable: true, get: function () { return __importDefault(usePinchZoom_1).default; } });
var useRendersCount_1 = require("./useRendersCount");
Object.defineProperty(exports, "useRendersCount", { enumerable: true, get: function () { return useRendersCount_1.useRendersCount; } });
var useFirstMountState_1 = require("./useFirstMountState");
Object.defineProperty(exports, "useFirstMountState", { enumerable: true, get: function () { return useFirstMountState_1.useFirstMountState; } });
var useSet_1 = require("./useSet");
Object.defineProperty(exports, "useSet", { enumerable: true, get: function () { return __importDefault(useSet_1).default; } });
var createGlobalState_1 = require("./factory/createGlobalState");
Object.defineProperty(exports, "createGlobalState", { enumerable: true, get: function () { return createGlobalState_1.createGlobalState; } });
var useHash_1 = require("./useHash");
Object.defineProperty(exports, "useHash", { enumerable: true, get: function () { return useHash_1.useHash; } });
