import React from 'react';
var createRouter = function () {
    var context = React.createContext({
        route: '',
    });
    // not sure if this supposed to be unused, ignoring ts error for now
    // @ts-ignore
    var Router = function (props) {
        var route = props.route, fullRoute = props.fullRoute, parent = props.parent, children = props.children;
        if (process.env.NODE_ENV !== 'production') {
            if (typeof route !== 'string') {
                throw new TypeError('Router route must be a string.');
            }
        }
        return React.createElement(context.Provider, {
            value: {
                fullRoute: fullRoute || route,
                route: route,
                parent: parent,
            },
            children: children,
        });
    };
};
export default createRouter;
