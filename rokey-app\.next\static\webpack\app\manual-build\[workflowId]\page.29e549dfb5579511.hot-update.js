"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction DocumentDuplicateIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75\"\n    }));\n}\n_c = DocumentDuplicateIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(DocumentDuplicateIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"DocumentDuplicateIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LinkSlashIcon.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/LinkSlashIcon.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction LinkSlashIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M13.181 8.68a4.503 4.503 0 0 1 1.903 6.405m-9.768-2.782L3.56 14.06a4.5 4.5 0 0 0 6.364 6.365l3.129-3.129m5.614-5.615 1.757-1.757a4.5 4.5 0 0 0-6.364-6.365l-4.5 4.5c-.258.26-.479.541-.661.84m1.903 6.405a4.495 4.495 0 0 1-1.242-.88 4.483 4.483 0 0 1-1.062-1.683m6.587 2.345 5.907 5.907m-5.907-5.907L8.898 8.898M2.991 2.99 8.898 8.9\"\n    }));\n}\n_c = LinkSlashIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(LinkSlashIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"LinkSlashIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LinkSlashIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction TrashIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n    }));\n}\n_c = TrashIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(TrashIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"TrashIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/manual-build/[workflowId]/page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkflowEditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react_dist_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react/dist/style.css */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/style.css\");\n/* harmony import */ var _components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/manual-build/WorkflowToolbar */ \"(app-pages-browser)/./src/components/manual-build/WorkflowToolbar.tsx\");\n/* harmony import */ var _components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/manual-build/NodePalette */ \"(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\");\n/* harmony import */ var _components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/manual-build/NodeConfigPanel */ \"(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\");\n/* harmony import */ var _components_manual_build_ContextMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/manual-build/ContextMenu */ \"(app-pages-browser)/./src/components/manual-build/ContextMenu.tsx\");\n/* harmony import */ var _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/manual-build/nodes */ \"(app-pages-browser)/./src/components/manual-build/nodes/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction WorkflowEditorPage(param) {\n    let { params } = param;\n    _s();\n    const resolvedParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const workflowId = resolvedParams === null || resolvedParams === void 0 ? void 0 : resolvedParams.workflowId;\n    const [workflow, setWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [nodes, setNodes, onNodesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useNodesState)([]);\n    const [edges, setEdges, onEdgesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useEdgesState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDirty, setIsDirty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load workflow data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkflowEditorPage.useEffect\": ()=>{\n            if (workflowId === 'new') {\n                initializeNewWorkflow();\n            } else {\n                loadWorkflow(workflowId);\n            }\n        }\n    }[\"WorkflowEditorPage.useEffect\"], [\n        workflowId\n    ]);\n    const initializeNewWorkflow = async ()=>{\n        try {\n            // Create default nodes for new workflow\n            const defaultNodes = [\n                {\n                    id: 'user-request',\n                    type: 'userRequest',\n                    position: {\n                        x: 50,\n                        y: 200\n                    },\n                    data: {\n                        label: 'User Request',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Starting point for user input'\n                    }\n                },\n                {\n                    id: 'classifier',\n                    type: 'classifier',\n                    position: {\n                        x: 350,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Classifier',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Analyzes and categorizes the request'\n                    }\n                },\n                {\n                    id: 'output',\n                    type: 'output',\n                    position: {\n                        x: 950,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Output',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Final response to the user'\n                    }\n                }\n            ];\n            const defaultEdges = [\n                {\n                    id: 'e1',\n                    source: 'user-request',\n                    target: 'classifier',\n                    type: 'smoothstep',\n                    animated: true\n                }\n            ];\n            setNodes(defaultNodes);\n            setEdges(defaultEdges);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to initialize new workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const loadWorkflow = async (id)=>{\n        try {\n            // TODO: Implement API call to load workflow\n            console.log('Loading workflow:', id);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to load workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const onConnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onConnect]\": (params)=>{\n            const newEdge = {\n                ...params,\n                id: \"e\".concat(edges.length + 1),\n                type: 'smoothstep',\n                animated: true\n            };\n            setEdges({\n                \"WorkflowEditorPage.useCallback[onConnect]\": (eds)=>(0,_xyflow_react__WEBPACK_IMPORTED_MODULE_10__.addEdge)(newEdge, eds)\n            }[\"WorkflowEditorPage.useCallback[onConnect]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[onConnect]\"], [\n        edges.length,\n        setEdges\n    ]);\n    const onNodeClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeClick]\": (event, node)=>{\n            setSelectedNode(node);\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeClick]\"], []);\n    const onPaneClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onPaneClick]\": ()=>{\n            setSelectedNode(null);\n            setContextMenu(null);\n        }\n    }[\"WorkflowEditorPage.useCallback[onPaneClick]\"], []);\n    const onNodeContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeContextMenu]\": (event, node)=>{\n            event.preventDefault();\n            setContextMenu({\n                id: node.id,\n                type: 'node',\n                nodeType: node.type,\n                x: event.clientX,\n                y: event.clientY\n            });\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeContextMenu]\"], []);\n    const onEdgeContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onEdgeContextMenu]\": (event, edge)=>{\n            event.preventDefault();\n            setContextMenu({\n                id: edge.id,\n                type: 'edge',\n                x: event.clientX,\n                y: event.clientY\n            });\n        }\n    }[\"WorkflowEditorPage.useCallback[onEdgeContextMenu]\"], []);\n    const handleDeleteNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (nodeId)=>{\n            // Don't delete core nodes\n            const coreNodes = [\n                'user-request',\n                'classifier',\n                'output'\n            ];\n            if (coreNodes.includes(nodeId)) return;\n            setNodes({\n                \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (nds)=>nds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (node)=>node.id !== nodeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"]);\n            setEdges({\n                \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (eds)=>eds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (edge)=>edge.source !== nodeId && edge.target !== nodeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"]);\n            setIsDirty(true);\n            // Close config panel if deleted node was selected\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"], [\n        selectedNode,\n        setNodes,\n        setEdges\n    ]);\n    const handleDeleteEdge = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (edgeId)=>{\n            setEdges({\n                \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (eds)=>eds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (edge)=>edge.id !== edgeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"], [\n        setEdges\n    ]);\n    const handleDuplicateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDuplicateNode]\": (nodeId)=>{\n            const nodeToDuplicate = nodes.find({\n                \"WorkflowEditorPage.useCallback[handleDuplicateNode].nodeToDuplicate\": (n)=>n.id === nodeId\n            }[\"WorkflowEditorPage.useCallback[handleDuplicateNode].nodeToDuplicate\"]);\n            if (!nodeToDuplicate) return;\n            const newNode = {\n                ...nodeToDuplicate,\n                id: \"\".concat(nodeToDuplicate.type, \"-\").concat(Date.now()),\n                position: {\n                    x: nodeToDuplicate.position.x + 50,\n                    y: nodeToDuplicate.position.y + 50\n                },\n                data: {\n                    ...nodeToDuplicate.data,\n                    label: \"\".concat(nodeToDuplicate.data.label, \" Copy\")\n                }\n            };\n            setNodes({\n                \"WorkflowEditorPage.useCallback[handleDuplicateNode]\": (nds)=>[\n                        ...nds,\n                        newNode\n                    ]\n            }[\"WorkflowEditorPage.useCallback[handleDuplicateNode]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDuplicateNode]\"], [\n        nodes,\n        setNodes\n    ]);\n    const handleConfigureNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleConfigureNode]\": (nodeId)=>{\n            const node = nodes.find({\n                \"WorkflowEditorPage.useCallback[handleConfigureNode].node\": (n)=>n.id === nodeId\n            }[\"WorkflowEditorPage.useCallback[handleConfigureNode].node\"]);\n            if (node) {\n                setSelectedNode(node);\n            }\n        }\n    }[\"WorkflowEditorPage.useCallback[handleConfigureNode]\"], [\n        nodes\n    ]);\n    const handleSave = async ()=>{\n        if (!workflow && workflowId === 'new') {\n            // Show save dialog for new workflow\n            const name = prompt('Enter workflow name:');\n            if (!name) return;\n            // TODO: Implement save new workflow\n            console.log('Saving new workflow:', name);\n        } else {\n            // Update existing workflow\n            setIsSaving(true);\n            try {\n                // TODO: Implement update workflow API call\n                console.log('Updating workflow:', workflowId);\n                setIsDirty(false);\n            } catch (error) {\n                console.error('Failed to save workflow:', error);\n            } finally{\n                setIsSaving(false);\n            }\n        }\n    };\n    const handleExecute = async ()=>{\n        // TODO: Implement workflow execution\n        console.log('Executing workflow');\n    };\n    const handleAddNode = (nodeType, position)=>{\n        const newNode = {\n            id: \"\".concat(nodeType, \"-\").concat(Date.now()),\n            type: nodeType,\n            position,\n            data: {\n                label: nodeType.charAt(0).toUpperCase() + nodeType.slice(1),\n                config: {},\n                isConfigured: false,\n                description: \"\".concat(nodeType, \" node\")\n            }\n        };\n        setNodes((nds)=>[\n                ...nds,\n                newNode\n            ]);\n        setIsDirty(true);\n    };\n    const handleNodeUpdate = (nodeId, updates)=>{\n        setNodes((nds)=>nds.map((node)=>node.id === nodeId ? {\n                    ...node,\n                    data: {\n                        ...node.data,\n                        ...updates\n                    }\n                } : node));\n        setIsDirty(true);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen bg-[#040716] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"Loading workflow...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#040716] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                workflow: workflow,\n                isDirty: isDirty,\n                isSaving: isSaving,\n                onSave: handleSave,\n                onExecute: handleExecute,\n                onBack: ()=>router.push('/manual-build')\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onAddNode: handleAddNode\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.ReactFlow, {\n                                nodes: nodes,\n                                edges: edges,\n                                onNodesChange: onNodesChange,\n                                onEdgesChange: onEdgesChange,\n                                onConnect: onConnect,\n                                onNodeClick: onNodeClick,\n                                onNodeContextMenu: onNodeContextMenu,\n                                onEdgeContextMenu: onEdgeContextMenu,\n                                onPaneClick: onPaneClick,\n                                nodeTypes: _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_8__.nodeTypes,\n                                fitView: true,\n                                className: \"bg-[#040716]\",\n                                defaultViewport: {\n                                    x: 0,\n                                    y: 0,\n                                    zoom: 0.8\n                                },\n                                connectionLineStyle: {\n                                    stroke: '#ff6b35',\n                                    strokeWidth: 2\n                                },\n                                defaultEdgeOptions: {\n                                    style: {\n                                        stroke: '#ff6b35',\n                                        strokeWidth: 2\n                                    },\n                                    type: 'smoothstep',\n                                    animated: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.Background, {\n                                        color: \"#1f2937\",\n                                        gap: 20,\n                                        size: 1,\n                                        variant: \"dots\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.Controls, {\n                                        className: \"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm\",\n                                        showInteractive: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.MiniMap, {\n                                        className: \"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm\",\n                                        nodeColor: \"#ff6b35\",\n                                        maskColor: \"rgba(0, 0, 0, 0.2)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            contextMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_ContextMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                id: contextMenu.id,\n                                type: contextMenu.type,\n                                nodeType: contextMenu.nodeType,\n                                top: contextMenu.y,\n                                left: contextMenu.x,\n                                onClose: ()=>setContextMenu(null),\n                                onDelete: contextMenu.type === 'node' ? handleDeleteNode : handleDeleteEdge,\n                                onDuplicate: contextMenu.type === 'node' ? handleDuplicateNode : undefined,\n                                onConfigure: contextMenu.type === 'node' ? handleConfigureNode : undefined,\n                                onDisconnect: contextMenu.type === 'edge' ? handleDeleteEdge : undefined\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this),\n                    selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        node: selectedNode,\n                        onUpdate: (updates)=>handleNodeUpdate(selectedNode.id, updates),\n                        onClose: ()=>setSelectedNode(null)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_s(WorkflowEditorPage, \"GiKoqOcAXo1NotOy4cq/1dWBc14=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useNodesState,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_9__.useEdgesState\n    ];\n});\n_c = WorkflowEditorPage;\nvar _c;\n$RefreshReg$(_c, \"WorkflowEditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/manual-build/ContextMenu.tsx":
/*!*****************************************************!*\
  !*** ./src/components/manual-build/ContextMenu.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContextMenu)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Cog6ToothIcon_DocumentDuplicateIcon_LinkSlashIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Cog6ToothIcon,DocumentDuplicateIcon,LinkSlashIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Cog6ToothIcon_DocumentDuplicateIcon_LinkSlashIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Cog6ToothIcon,DocumentDuplicateIcon,LinkSlashIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Cog6ToothIcon_DocumentDuplicateIcon_LinkSlashIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cog6ToothIcon,DocumentDuplicateIcon,LinkSlashIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Cog6ToothIcon_DocumentDuplicateIcon_LinkSlashIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Cog6ToothIcon,DocumentDuplicateIcon,LinkSlashIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LinkSlashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ContextMenu(param) {\n    let { id, top, left, right, bottom, type, nodeType, onClose, onDelete, onDuplicate, onConfigure, onDisconnect } = param;\n    _s();\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ContextMenu.useCallback[handleAction]\": (action)=>{\n            action();\n            onClose();\n        }\n    }[\"ContextMenu.useCallback[handleAction]\"], [\n        onClose\n    ]);\n    // Check if node can be deleted (not core nodes)\n    const canDelete = type === 'edge' || ![\n        'userRequest',\n        'classifier',\n        'output'\n    ].includes(nodeType || '');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed z-50 bg-gray-800 border border-gray-700 rounded-lg shadow-xl py-1 min-w-[160px]\",\n                style: {\n                    top: bottom ? undefined : top,\n                    left: right ? undefined : left,\n                    right: right ? window.innerWidth - right : undefined,\n                    bottom: bottom ? window.innerHeight - bottom : undefined\n                },\n                children: [\n                    type === 'node' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            onConfigure && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAction(()=>onConfigure(id)),\n                                className: \"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cog6ToothIcon_DocumentDuplicateIcon_LinkSlashIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Configure\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this),\n                            onDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAction(()=>onDuplicate(id)),\n                                className: \"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cog6ToothIcon_DocumentDuplicateIcon_LinkSlashIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Duplicate\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this),\n                            canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 my-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAction(()=>onDelete(id)),\n                                        className: \"w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cog6ToothIcon_DocumentDuplicateIcon_LinkSlashIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Delete Node\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            !canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-700 my-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-2 text-xs text-gray-500\",\n                                        children: \"Core nodes cannot be deleted\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true),\n                    type === 'edge' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            onDisconnect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAction(()=>onDisconnect(id)),\n                                className: \"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cog6ToothIcon_DocumentDuplicateIcon_LinkSlashIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Disconnect\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-700 my-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAction(()=>onDelete(id)),\n                                className: \"w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cog6ToothIcon_DocumentDuplicateIcon_LinkSlashIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Delete Connection\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\ContextMenu.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ContextMenu, \"lo9iZe4kKXTV4QkOEgV5A6nFfbU=\");\n_c = ContextMenu;\nvar _c;\n$RefreshReg$(_c, \"ContextMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/ContextMenu.tsx\n"));

/***/ })

});