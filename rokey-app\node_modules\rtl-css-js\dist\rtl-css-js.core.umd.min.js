!function(r,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((r=r||self).rtlCSSJSCore={})}(this,(function(r){"use strict";function t(r){return r.reduce((function(r,t){var e=t[0],n=t[1];return r[e]=n,r[n]=e,r}),{})}function e(r){return"boolean"==typeof r}function n(r){return"function"==typeof r}function o(r){return"number"==typeof r}function i(r){return null==r}function a(r){return r&&"object"==typeof r}function s(r){return"string"==typeof r}function u(r,t){return-1!==r.indexOf(t)}function l(r){return 0===parseFloat(r)?r:"-"===r[0]?r.slice(1):"-"+r}function g(r,t,e,n){return t+l(e)+n}function d(r){var t=r.indexOf(".");if(-1===t)r=100-parseFloat(r)+"%";else{var e=r.length-t-2;r=(r=100-parseFloat(r)).toFixed(e)+"%"}return r}function f(r){return r.replace(/ +/g," ").split(" ").map((function(r){return r.trim()})).filter(Boolean).reduce((function(r,t){var e=r.list,n=r.state,o=(t.match(/\(/g)||[]).length,i=(t.match(/\)/g)||[]).length;return n.parensDepth>0?e[e.length-1]=e[e.length-1]+" "+t:e.push(t),n.parensDepth+=o-i,{list:e,state:n}}),{list:[],state:{parensDepth:0}}).list}function c(r){var t=f(r);if(t.length<=3||t.length>4)return r;var e=t[0],n=t[1],o=t[2];return[e,t[3],o,n].join(" ")}function p(r){return!e(r)&&!i(r)}function b(r){for(var t=[],e=0,n=0,o=!1;n<r.length;)o||","!==r[n]?"("===r[n]?(o=!0,n++):")"===r[n]?(o=!1,n++):n++:(t.push(r.substring(e,n).trim()),e=++n);return e!=n&&t.push(r.substring(e,n+1)),t}var m={padding:function(r){var t=r.value;return o(t)?t:c(t)},textShadow:function(r){return b(r.value).map((function(r){return r.replace(/(^|\s)(-*)([.|\d]+)/,(function(r,t,e,n){return"0"===n?r:""+t+(""===e?"-":"")+n}))})).join(",")},borderColor:function(r){return c(r.value)},borderRadius:function(r){var t=r.value;if(o(t))return t;if(u(t,"/")){var e=t.split("/"),n=e[0],i=e[1];return m.borderRadius({value:n.trim()})+" / "+m.borderRadius({value:i.trim()})}var a=f(t);switch(a.length){case 2:return a.reverse().join(" ");case 4:var s=a[0],l=a[1],g=a[2];return[l,s,a[3],g].join(" ");default:return t}},background:function(r){var t=r.value,e=r.valuesToConvert,n=r.isRtl,i=r.bgImgDirectionRegex,a=r.bgPosDirectionRegex;if(o(t))return t;var s=t.replace(/(url\(.*?\))|(rgba?\(.*?\))|(hsl\(.*?\))|(#[a-fA-F0-9]+)|((^| )(\D)+( |$))/g,"").trim();return t=t.replace(s,m.backgroundPosition({value:s,valuesToConvert:e,isRtl:n,bgPosDirectionRegex:a})),m.backgroundImage({value:t,valuesToConvert:e,bgImgDirectionRegex:i})},backgroundImage:function(r){var t=r.value,e=r.valuesToConvert,n=r.bgImgDirectionRegex;return u(t,"url(")||u(t,"linear-gradient(")?t.replace(n,(function(r,t,n){return r.replace(n,e[n])})):t},backgroundPosition:function(r){var t=r.value,e=r.valuesToConvert,n=r.isRtl,o=r.bgPosDirectionRegex;return t.replace(n?/^((-|\d|\.)+%)/:null,(function(r,t){return d(t)})).replace(o,(function(r){return e[r]}))},backgroundPositionX:function(r){var t=r.value,e=r.valuesToConvert,n=r.isRtl,i=r.bgPosDirectionRegex;return o(t)?t:m.backgroundPosition({value:t,valuesToConvert:e,isRtl:n,bgPosDirectionRegex:i})},transition:function(r){var t=r.value,e=r.propertiesToConvert;return t.split(/,\s*/g).map((function(r){var t=r.split(" ");return t[0]=e[t[0]]||t[0],t.join(" ")})).join(", ")},transitionProperty:function(r){var t=r.value,e=r.propertiesToConvert;return t.split(/,\s*/g).map((function(r){return e[r]||r})).join(", ")},transform:function(r){var t=r.value,e="(?:(?:(?:\\[0-9a-f]{1,6})(?:\\r\\n|\\s)?)|\\\\[^\\r\\n\\f0-9a-f])",n="((?:-?(?:[0-9]*\\.[0-9]+|[0-9]+)(?:\\s*(?:em|ex|px|cm|mm|in|pt|pc|deg|rad|grad|ms|s|hz|khz|%)|-?(?:[_a-z]|[^\\u0020-\\u007e]|"+e+")(?:[_a-z0-9-]|[^\\u0020-\\u007e]|"+e+")*)?)|(?:inherit|auto))",o=new RegExp("(translateX\\s*\\(\\s*)"+n+"(\\s*\\))","gi"),i=new RegExp("(translate\\s*\\(\\s*)"+n+"((?:\\s*,\\s*"+n+"){0,1}\\s*\\))","gi"),a=new RegExp("(translate3d\\s*\\(\\s*)"+n+"((?:\\s*,\\s*"+n+"){0,2}\\s*\\))","gi"),s=new RegExp("(rotate[ZY]?\\s*\\(\\s*)"+n+"(\\s*\\))","gi");return t.replace(o,g).replace(i,g).replace(a,g).replace(s,g)}};m.objectPosition=m.backgroundPosition,m.margin=m.padding,m.borderWidth=m.padding,m.boxShadow=m.textShadow,m.webkitBoxShadow=m.boxShadow,m.mozBoxShadow=m.boxShadow,m.WebkitBoxShadow=m.boxShadow,m.MozBoxShadow=m.boxShadow,m.borderStyle=m.borderColor,m.webkitTransform=m.transform,m.mozTransform=m.transform,m.WebkitTransform=m.transform,m.MozTransform=m.transform,m.transformOrigin=m.backgroundPosition,m.webkitTransformOrigin=m.transformOrigin,m.mozTransformOrigin=m.transformOrigin,m.WebkitTransformOrigin=m.transformOrigin,m.MozTransformOrigin=m.transformOrigin,m.webkitTransition=m.transition,m.mozTransition=m.transition,m.WebkitTransition=m.transition,m.MozTransition=m.transition,m.webkitTransitionProperty=m.transitionProperty,m.mozTransitionProperty=m.transitionProperty,m.WebkitTransitionProperty=m.transitionProperty,m.MozTransitionProperty=m.transitionProperty,m["text-shadow"]=m.textShadow,m["border-color"]=m.borderColor,m["border-radius"]=m.borderRadius,m["background-image"]=m.backgroundImage,m["background-position"]=m.backgroundPosition,m["background-position-x"]=m.backgroundPositionX,m["object-position"]=m.objectPosition,m["border-width"]=m.padding,m["box-shadow"]=m.textShadow,m["-webkit-box-shadow"]=m.textShadow,m["-moz-box-shadow"]=m.textShadow,m["border-style"]=m.borderColor,m["-webkit-transform"]=m.transform,m["-moz-transform"]=m.transform,m["transform-origin"]=m.transformOrigin,m["-webkit-transform-origin"]=m.transformOrigin,m["-moz-transform-origin"]=m.transformOrigin,m["-webkit-transition"]=m.transition,m["-moz-transition"]=m.transition,m["transition-property"]=m.transitionProperty,m["-webkit-transition-property"]=m.transitionProperty,m["-moz-transition-property"]=m.transitionProperty;var v=t([["paddingLeft","paddingRight"],["marginLeft","marginRight"],["left","right"],["borderLeft","borderRight"],["borderLeftColor","borderRightColor"],["borderLeftStyle","borderRightStyle"],["borderLeftWidth","borderRightWidth"],["borderTopLeftRadius","borderTopRightRadius"],["borderBottomLeftRadius","borderBottomRightRadius"],["padding-left","padding-right"],["margin-left","margin-right"],["border-left","border-right"],["border-left-color","border-right-color"],["border-left-style","border-right-style"],["border-left-width","border-right-width"],["border-top-left-radius","border-top-right-radius"],["border-bottom-left-radius","border-bottom-right-radius"]]),h=["content"],w=t([["ltr","rtl"],["left","right"],["w-resize","e-resize"],["sw-resize","se-resize"],["nw-resize","ne-resize"]]),x=new RegExp("(^|\\W|_)((ltr)|(rtl)|(left)|(right))(\\W|_|$)","g"),y=new RegExp("(left)|(right)");function R(r){return Object.keys(r).reduce((function(t,e){var n=r[e];if(s(n)&&(n=n.trim()),u(h,e))return t[e]=n,t;var o=k(e,n),i=o.key,a=o.value;return t[i]=a,t}),Array.isArray(r)?[]:{})}function k(r,t){var e=/\/\*\s?@noflip\s?\*\//.test(t),n=e?r:T(r);return{key:n,value:e?t:P(n,t)}}function T(r){return v[r]||r}function P(r,t){if(!p(t))return t;if(a(t))return R(t);var e,i=o(t),s=n(t),u=i||s?t:t.replace(/ !important.*?$/,""),l=!i&&u.length!==t.length,g=m[r];return e=g?g({value:u,valuesToConvert:w,propertiesToConvert:v,isRtl:!0,bgImgDirectionRegex:x,bgPosDirectionRegex:y}):w[u]||u,l?e+" !important":e}r.arrayToObject=t,r.calculateNewBackgroundPosition=d,r.calculateNewTranslate=g,r.canConvertValue=p,r.convert=R,r.convertProperty=k,r.flipSign=l,r.flipTransformSign=g,r.getPropertyDoppelganger=T,r.getValueDoppelganger=P,r.getValuesAsList=f,r.handleQuartetValues=c,r.includes=u,r.isBoolean=e,r.isFunction=n,r.isNullOrUndefined=i,r.isNumber=o,r.isObject=a,r.isString=s,r.propertiesToConvert=v,r.propertyValueConverters=m,r.propsToIgnore=h,r.splitShadow=b,r.valuesToConvert=w,Object.defineProperty(r,"__esModule",{value:!0})}));
//# sourceMappingURL=rtl-css-js.core.umd.min.js.map
