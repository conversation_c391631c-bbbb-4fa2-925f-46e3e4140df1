"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/manual-build/[workflowId]/page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkflowEditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react_dist_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react/dist/style.css */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/style.css\");\n/* harmony import */ var _components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/manual-build/WorkflowToolbar */ \"(app-pages-browser)/./src/components/manual-build/WorkflowToolbar.tsx\");\n/* harmony import */ var _components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/manual-build/NodePalette */ \"(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\");\n/* harmony import */ var _components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/manual-build/NodeConfigPanel */ \"(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\");\n/* harmony import */ var _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/manual-build/nodes */ \"(app-pages-browser)/./src/components/manual-build/nodes/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction WorkflowEditorPage(param) {\n    let { params } = param;\n    _s();\n    const resolvedParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const workflowId = resolvedParams === null || resolvedParams === void 0 ? void 0 : resolvedParams.workflowId;\n    const [workflow, setWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [nodes, setNodes, onNodesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.useNodesState)([]);\n    const [edges, setEdges, onEdgesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.useEdgesState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDirty, setIsDirty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load workflow data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkflowEditorPage.useEffect\": ()=>{\n            if (workflowId === 'new') {\n                initializeNewWorkflow();\n            } else {\n                loadWorkflow(workflowId);\n            }\n        }\n    }[\"WorkflowEditorPage.useEffect\"], [\n        workflowId\n    ]);\n    const initializeNewWorkflow = async ()=>{\n        try {\n            // Create default nodes for new workflow\n            const defaultNodes = [\n                {\n                    id: 'user-request',\n                    type: 'userRequest',\n                    position: {\n                        x: 50,\n                        y: 200\n                    },\n                    data: {\n                        label: 'User Request',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Starting point for user input'\n                    }\n                },\n                {\n                    id: 'classifier',\n                    type: 'classifier',\n                    position: {\n                        x: 350,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Classifier',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Analyzes and categorizes the request'\n                    }\n                },\n                {\n                    id: 'output',\n                    type: 'output',\n                    position: {\n                        x: 950,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Output',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Final response to the user'\n                    }\n                }\n            ];\n            const defaultEdges = [\n                {\n                    id: 'e1',\n                    source: 'user-request',\n                    target: 'classifier',\n                    type: 'smoothstep',\n                    animated: true\n                }\n            ];\n            setNodes(defaultNodes);\n            setEdges(defaultEdges);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to initialize new workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const loadWorkflow = async (id)=>{\n        try {\n            // TODO: Implement API call to load workflow\n            console.log('Loading workflow:', id);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to load workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const onConnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onConnect]\": (params)=>{\n            const newEdge = {\n                ...params,\n                id: \"e\".concat(edges.length + 1),\n                type: 'smoothstep',\n                animated: true\n            };\n            setEdges({\n                \"WorkflowEditorPage.useCallback[onConnect]\": (eds)=>(0,_xyflow_react__WEBPACK_IMPORTED_MODULE_9__.addEdge)(newEdge, eds)\n            }[\"WorkflowEditorPage.useCallback[onConnect]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[onConnect]\"], [\n        edges.length,\n        setEdges\n    ]);\n    const onNodeClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeClick]\": (event, node)=>{\n            setSelectedNode(node);\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeClick]\"], []);\n    const onPaneClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onPaneClick]\": ()=>{\n            setSelectedNode(null);\n        }\n    }[\"WorkflowEditorPage.useCallback[onPaneClick]\"], []);\n    const handleSave = async ()=>{\n        if (!workflow && workflowId === 'new') {\n            // Show save dialog for new workflow\n            const name = prompt('Enter workflow name:');\n            if (!name) return;\n            // TODO: Implement save new workflow\n            console.log('Saving new workflow:', name);\n        } else {\n            // Update existing workflow\n            setIsSaving(true);\n            try {\n                // TODO: Implement update workflow API call\n                console.log('Updating workflow:', workflowId);\n                setIsDirty(false);\n            } catch (error) {\n                console.error('Failed to save workflow:', error);\n            } finally{\n                setIsSaving(false);\n            }\n        }\n    };\n    const handleExecute = async ()=>{\n        // TODO: Implement workflow execution\n        console.log('Executing workflow');\n    };\n    const handleAddNode = (nodeType, position)=>{\n        const newNode = {\n            id: \"\".concat(nodeType, \"-\").concat(Date.now()),\n            type: nodeType,\n            position,\n            data: {\n                label: nodeType.charAt(0).toUpperCase() + nodeType.slice(1),\n                config: {},\n                isConfigured: false,\n                description: \"\".concat(nodeType, \" node\")\n            }\n        };\n        setNodes((nds)=>[\n                ...nds,\n                newNode\n            ]);\n        setIsDirty(true);\n    };\n    const handleNodeUpdate = (nodeId, updates)=>{\n        setNodes((nds)=>nds.map((node)=>node.id === nodeId ? {\n                    ...node,\n                    data: {\n                        ...node.data,\n                        ...updates\n                    }\n                } : node));\n        setIsDirty(true);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen bg-[#040716] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"Loading workflow...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#040716] flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                workflow: workflow,\n                isDirty: isDirty,\n                isSaving: isSaving,\n                onSave: handleSave,\n                onExecute: handleExecute,\n                onBack: ()=>router.push('/manual-build')\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onAddNode: handleAddNode\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.ReactFlow, {\n                            nodes: nodes,\n                            edges: edges,\n                            onNodesChange: onNodesChange,\n                            onEdgesChange: onEdgesChange,\n                            onConnect: onConnect,\n                            onNodeClick: onNodeClick,\n                            onPaneClick: onPaneClick,\n                            nodeTypes: _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_7__.nodeTypes,\n                            fitView: true,\n                            className: \"bg-[#040716]\",\n                            defaultViewport: {\n                                x: 0,\n                                y: 0,\n                                zoom: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.Background, {\n                                    color: \"#1f2937\",\n                                    gap: 20,\n                                    size: 1,\n                                    variant: \"dots\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.Controls, {\n                                    className: \"bg-gray-800 border border-gray-700\",\n                                    showInteractive: false\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_8__.MiniMap, {\n                                    className: \"bg-gray-800 border border-gray-700\",\n                                    nodeColor: \"#ff6b35\",\n                                    maskColor: \"rgba(0, 0, 0, 0.2)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        node: selectedNode,\n                        onUpdate: (updates)=>handleNodeUpdate(selectedNode.id, updates),\n                        onClose: ()=>setSelectedNode(null)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\n_s(WorkflowEditorPage, \"chigew/JMYUPpxPkUMp7V/jqz7I=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_8__.useNodesState,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_8__.useEdgesState\n    ];\n});\n_c = WorkflowEditorPage;\nvar _c;\n$RefreshReg$(_c, \"WorkflowEditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx\n"));

/***/ })

});