{"version": 3, "file": "rtl-css-js.umd.js", "sources": ["../src/internal/utils.js", "../src/internal/property-value-converters.js", "../src/internal/convert.js"], "sourcesContent": ["/**\n * Takes an array of [keyValue1, keyValue2] pairs and creates an object of {keyValue1: keyValue2, keyValue2: keyValue1}\n * @param {Array} array the array of pairs\n * @return {Object} the {key, value} pair object\n */\nfunction arrayToObject(array) {\n  return array.reduce((obj, [prop1, prop2]) => {\n    obj[prop1] = prop2\n    obj[prop2] = prop1\n    return obj\n  }, {})\n}\n\nfunction isBoolean(val) {\n  return typeof val === 'boolean'\n}\n\nfunction isFunction(val) {\n  return typeof val === 'function'\n}\n\nfunction isNumber(val) {\n  return typeof val === 'number'\n}\n\nfunction isNullOrUndefined(val) {\n  return val === null || typeof val === 'undefined'\n}\n\nfunction isObject(val) {\n  return val && typeof val === 'object'\n}\n\nfunction isString(val) {\n  return typeof val === 'string'\n}\n\nfunction includes(inclusive, inclusee) {\n  return inclusive.indexOf(inclusee) !== -1\n}\n\n/**\n * Flip the sign of a CSS value, possibly with a unit.\n *\n * We can't just negate the value with unary minus due to the units.\n *\n * @private\n * @param {String} value - the original value (for example 77%)\n * @return {String} the result (for example -77%)\n */\nfunction flipSign(value) {\n  if (parseFloat(value) === 0) {\n    // Don't mangle zeroes\n    return value\n  }\n\n  if (value[0] === '-') {\n    return value.slice(1)\n  }\n\n  return `-${value}`\n}\n\nfunction flipTransformSign(match, prefix, offset, suffix) {\n  return prefix + flipSign(offset) + suffix\n}\n\n/**\n * Takes a percentage for background position and inverts it.\n * This was copied and modified from CSSJanus:\n * https://github.com/cssjanus/cssjanus/blob/4245f834365f6cfb0239191a151432fb85abab23/src/cssjanus.js#L152-L175\n * @param {String} value - the original value (for example 77%)\n * @return {String} the result (for example 23%)\n */\nfunction calculateNewBackgroundPosition(value) {\n  const idx = value.indexOf('.')\n  if (idx === -1) {\n    value = `${100 - parseFloat(value)}%`\n  } else {\n    // Two off, one for the \"%\" at the end, one for the dot itself\n    const len = value.length - idx - 2\n    value = 100 - parseFloat(value)\n    value = `${value.toFixed(len)}%`\n  }\n  return value\n}\n\n/**\n * This takes a list of CSS values and converts it to an array\n * @param {String} value - something like `1px`, `1px 2em`, or `3pt rgb(150, 230, 550) 40px calc(100% - 5px)`\n * @return {Array} the split values (for example: `['3pt', 'rgb(150, 230, 550)', '40px', 'calc(100% - 5px)']`)\n */\nfunction getValuesAsList(value) {\n  return (\n    value\n      .replace(/ +/g, ' ') // remove all extraneous spaces\n      .split(' ')\n      .map(i => i.trim()) // get rid of extra space before/after each item\n      .filter(Boolean) // get rid of empty strings\n      // join items which are within parenthese\n      // luckily `calc (100% - 5px)` is invalid syntax and it must be `calc(100% - 5px)`, otherwise this would be even more complex\n      .reduce(\n        ({list, state}, item) => {\n          const openParansCount = (item.match(/\\(/g) || []).length\n          const closedParansCount = (item.match(/\\)/g) || []).length\n          if (state.parensDepth > 0) {\n            list[list.length - 1] = `${list[list.length - 1]} ${item}`\n          } else {\n            list.push(item)\n          }\n          state.parensDepth += openParansCount - closedParansCount\n          return {list, state}\n        },\n        {list: [], state: {parensDepth: 0}},\n      ).list\n  )\n}\n\n/**\n * This is intended for properties that are `top right bottom left` and will switch them to `top left bottom right`\n * @param {String} value - `1px 2px 3px 4px` for example, but also handles cases where there are too few/too many and\n * simply returns the value in those cases (which is the correct behavior)\n * @return {String} the result - `1px 4px 3px 2px` for example.\n */\nfunction handleQuartetValues(value) {\n  const splitValues = getValuesAsList(value)\n  if (splitValues.length <= 3 || splitValues.length > 4) {\n    return value\n  }\n  const [top, right, bottom, left] = splitValues\n  return [top, left, bottom, right].join(' ')\n}\n\n/**\n *\n * @param {String|Number|Object} value css property value to test\n * @returns If the css property value can(should?) have an RTL equivalent\n */\nfunction canConvertValue(value) {\n  return !isBoolean(value) && !isNullOrUndefined(value)\n}\n\n/**\n * Splits a shadow style into its separate shadows using the comma delimiter, but creating an exception\n * for comma separated values in parentheses often used for rgba colours.\n * @param {String} value\n * @returns {Array} array of all box shadow values in the string\n */\nfunction splitShadow(value) {\n  const shadows = []\n  let start = 0\n  let end = 0\n  let rgba = false\n  while (end < value.length) {\n    if (!rgba && value[end] === ',') {\n      shadows.push(value.substring(start, end).trim())\n      end++\n      start = end\n    } else if (value[end] === `(`) {\n      rgba = true\n      end++\n    } else if (value[end] === ')') {\n      rgba = false\n      end++\n    } else {\n      end++\n    }\n  }\n\n  // push the last shadow value if there is one\n  // istanbul ignore next\n  if (start != end) {\n    shadows.push(value.substring(start, end + 1))\n  }\n\n  return shadows\n}\n\nexport {\n  arrayToObject,\n  calculateNewBackgroundPosition,\n  canConvertValue,\n  flipTransformSign as calculateNewTranslate,\n  flipTransformSign,\n  flipSign,\n  handleQuartetValues,\n  includes,\n  isBoolean,\n  isFunction,\n  isNumber,\n  isNullOrUndefined,\n  isObject,\n  isString,\n  getValuesAsList,\n  splitShadow,\n}\n", "import {\n  includes,\n  isNumber,\n  calculateNewBackgroundPosition,\n  flipTransformSign,\n  handleQuartetValues,\n  getValuesAsList,\n  splitShadow,\n} from './utils'\n\n// some values require a little fudging, that fudging goes here.\nconst propertyValueConverters = {\n  padding({value}) {\n    if (isNumber(value)) {\n      return value\n    }\n    return handleQuartetValues(value)\n  },\n  textShadow({value}) {\n    const flippedShadows = splitShadow(value).map(shadow => {\n      // intentionally leaving off the `g` flag here because we only want to change the first number (which is the offset-x)\n      return shadow.replace(\n        /(^|\\s)(-*)([.|\\d]+)/,\n        (match, whiteSpace, negative, number) => {\n          if (number === '0') {\n            return match\n          }\n          const doubleNegative = negative === '' ? '-' : ''\n          return `${whiteSpace}${doubleNegative}${number}`\n        },\n      )\n    })\n\n    return flippedShadows.join(',')\n  },\n  borderColor({value}) {\n    return handleQuartetValues(value)\n  },\n  borderRadius({value}) {\n    if (isNumber(value)) {\n      return value\n    }\n    if (includes(value, '/')) {\n      const [radius1, radius2] = value.split('/')\n      const convertedRadius1 = propertyValueConverters.borderRadius({\n        value: radius1.trim(),\n      })\n      const convertedRadius2 = propertyValueConverters.borderRadius({\n        value: radius2.trim(),\n      })\n      return `${convertedRadius1} / ${convertedRadius2}`\n    }\n    const splitValues = getValuesAsList(value)\n    switch (splitValues.length) {\n      case 2: {\n        return splitValues.reverse().join(' ')\n      }\n      case 4: {\n        const [topLeft, topRight, bottomRight, bottomLeft] = splitValues\n        return [topRight, topLeft, bottomLeft, bottomRight].join(' ')\n      }\n      default: {\n        return value\n      }\n    }\n  },\n  background({\n    value,\n    valuesToConvert,\n    isRtl,\n    bgImgDirectionRegex,\n    bgPosDirectionRegex,\n  }) {\n    if (isNumber(value)) {\n      return value\n    }\n\n    // Yeah, this is in need of a refactor 🙃...\n    // but this property is a tough cookie 🍪\n    // get the backgroundPosition out of the string by removing everything that couldn't be the backgroundPosition value\n    const backgroundPositionValue = value\n      .replace(\n        /(url\\(.*?\\))|(rgba?\\(.*?\\))|(hsl\\(.*?\\))|(#[a-fA-F0-9]+)|((^| )(\\D)+( |$))/g,\n        '',\n      )\n      .trim()\n    // replace that backgroundPosition value with the converted version\n    value = value.replace(\n      backgroundPositionValue,\n      propertyValueConverters.backgroundPosition({\n        value: backgroundPositionValue,\n        valuesToConvert,\n        isRtl,\n        bgPosDirectionRegex,\n      }),\n    )\n    // do the backgroundImage value replacing on the whole value (because why not?)\n    return propertyValueConverters.backgroundImage({\n      value,\n      valuesToConvert,\n      bgImgDirectionRegex,\n    })\n  },\n  backgroundImage({value, valuesToConvert, bgImgDirectionRegex}) {\n    if (!includes(value, 'url(') && !includes(value, 'linear-gradient(')) {\n      return value\n    }\n    return value.replace(bgImgDirectionRegex, (match, g1, group2) => {\n      return match.replace(group2, valuesToConvert[group2])\n    })\n  },\n  backgroundPosition({value, valuesToConvert, isRtl, bgPosDirectionRegex}) {\n    return (\n      value\n        // intentionally only grabbing the first instance of this because that represents `left`\n        .replace(isRtl ? /^((-|\\d|\\.)+%)/ : null, (match, group) =>\n          calculateNewBackgroundPosition(group),\n        )\n        .replace(bgPosDirectionRegex, match => valuesToConvert[match])\n    )\n  },\n  backgroundPositionX({value, valuesToConvert, isRtl, bgPosDirectionRegex}) {\n    if (isNumber(value)) {\n      return value\n    }\n    return propertyValueConverters.backgroundPosition({\n      value,\n      valuesToConvert,\n      isRtl,\n      bgPosDirectionRegex,\n    })\n  },\n  transition({value, propertiesToConvert}) {\n    return value\n      .split(/,\\s*/g)\n      .map(transition => {\n        const values = transition.split(' ')\n\n        // Property is always defined first\n        values[0] = propertiesToConvert[values[0]] || values[0]\n\n        return values.join(' ')\n      })\n      .join(', ')\n  },\n  transitionProperty({value, propertiesToConvert}) {\n    return value\n      .split(/,\\s*/g)\n      .map(prop => propertiesToConvert[prop] || prop)\n      .join(', ')\n  },\n  transform({value}) {\n    // This was copied and modified from CSSJanus:\n    // https://github.com/cssjanus/cssjanus/blob/4a40f001b1ba35567112d8b8e1d9d95eda4234c3/src/cssjanus.js#L152-L153\n    const nonAsciiPattern = '[^\\\\u0020-\\\\u007e]'\n    const unicodePattern = '(?:(?:\\\\[0-9a-f]{1,6})(?:\\\\r\\\\n|\\\\s)?)'\n    const numPattern = '(?:[0-9]*\\\\.[0-9]+|[0-9]+)'\n    const unitPattern = '(?:em|ex|px|cm|mm|in|pt|pc|deg|rad|grad|ms|s|hz|khz|%)'\n    const escapePattern = `(?:${unicodePattern}|\\\\\\\\[^\\\\r\\\\n\\\\f0-9a-f])`\n    const nmstartPattern = `(?:[_a-z]|${nonAsciiPattern}|${escapePattern})`\n    const nmcharPattern = `(?:[_a-z0-9-]|${nonAsciiPattern}|${escapePattern})`\n    const identPattern = `-?${nmstartPattern}${nmcharPattern}*`\n    const quantPattern = `${numPattern}(?:\\\\s*${unitPattern}|${identPattern})?`\n    const signedQuantPattern = `((?:-?${quantPattern})|(?:inherit|auto))`\n    const translateXRegExp = new RegExp(\n      `(translateX\\\\s*\\\\(\\\\s*)${signedQuantPattern}(\\\\s*\\\\))`,\n      'gi',\n    )\n    const translateRegExp = new RegExp(\n      `(translate\\\\s*\\\\(\\\\s*)${signedQuantPattern}((?:\\\\s*,\\\\s*${signedQuantPattern}){0,1}\\\\s*\\\\))`,\n      'gi',\n    )\n    const translate3dRegExp = new RegExp(\n      `(translate3d\\\\s*\\\\(\\\\s*)${signedQuantPattern}((?:\\\\s*,\\\\s*${signedQuantPattern}){0,2}\\\\s*\\\\))`,\n      'gi',\n    )\n    const rotateRegExp = new RegExp(\n      `(rotate[ZY]?\\\\s*\\\\(\\\\s*)${signedQuantPattern}(\\\\s*\\\\))`,\n      'gi',\n    )\n    return value\n      .replace(translateXRegExp, flipTransformSign)\n      .replace(translateRegExp, flipTransformSign)\n      .replace(translate3dRegExp, flipTransformSign)\n      .replace(rotateRegExp, flipTransformSign)\n  },\n}\n\npropertyValueConverters.objectPosition =\n  propertyValueConverters.backgroundPosition\npropertyValueConverters.margin = propertyValueConverters.padding\npropertyValueConverters.borderWidth = propertyValueConverters.padding\npropertyValueConverters.boxShadow = propertyValueConverters.textShadow\npropertyValueConverters.webkitBoxShadow = propertyValueConverters.boxShadow\npropertyValueConverters.mozBoxShadow = propertyValueConverters.boxShadow\npropertyValueConverters.WebkitBoxShadow = propertyValueConverters.boxShadow\npropertyValueConverters.MozBoxShadow = propertyValueConverters.boxShadow\npropertyValueConverters.borderStyle = propertyValueConverters.borderColor\npropertyValueConverters.webkitTransform = propertyValueConverters.transform\npropertyValueConverters.mozTransform = propertyValueConverters.transform\npropertyValueConverters.WebkitTransform = propertyValueConverters.transform\npropertyValueConverters.MozTransform = propertyValueConverters.transform\npropertyValueConverters.transformOrigin =\n  propertyValueConverters.backgroundPosition\npropertyValueConverters.webkitTransformOrigin =\n  propertyValueConverters.transformOrigin\npropertyValueConverters.mozTransformOrigin =\n  propertyValueConverters.transformOrigin\npropertyValueConverters.WebkitTransformOrigin =\n  propertyValueConverters.transformOrigin\npropertyValueConverters.MozTransformOrigin =\n  propertyValueConverters.transformOrigin\npropertyValueConverters.webkitTransition = propertyValueConverters.transition\npropertyValueConverters.mozTransition = propertyValueConverters.transition\npropertyValueConverters.WebkitTransition = propertyValueConverters.transition\npropertyValueConverters.MozTransition = propertyValueConverters.transition\npropertyValueConverters.webkitTransitionProperty =\n  propertyValueConverters.transitionProperty\npropertyValueConverters.mozTransitionProperty =\n  propertyValueConverters.transitionProperty\npropertyValueConverters.WebkitTransitionProperty =\n  propertyValueConverters.transitionProperty\npropertyValueConverters.MozTransitionProperty =\n  propertyValueConverters.transitionProperty\n\n// kebab-case versions\n\npropertyValueConverters['text-shadow'] = propertyValueConverters.textShadow\npropertyValueConverters['border-color'] = propertyValueConverters.borderColor\npropertyValueConverters['border-radius'] = propertyValueConverters.borderRadius\npropertyValueConverters['background-image'] =\n  propertyValueConverters.backgroundImage\npropertyValueConverters['background-position'] =\n  propertyValueConverters.backgroundPosition\npropertyValueConverters['background-position-x'] =\n  propertyValueConverters.backgroundPositionX\npropertyValueConverters['object-position'] =\n  propertyValueConverters.objectPosition\npropertyValueConverters['border-width'] = propertyValueConverters.padding\npropertyValueConverters['box-shadow'] = propertyValueConverters.textShadow\npropertyValueConverters['-webkit-box-shadow'] =\n  propertyValueConverters.textShadow\npropertyValueConverters['-moz-box-shadow'] = propertyValueConverters.textShadow\npropertyValueConverters['border-style'] = propertyValueConverters.borderColor\npropertyValueConverters['-webkit-transform'] = propertyValueConverters.transform\npropertyValueConverters['-moz-transform'] = propertyValueConverters.transform\npropertyValueConverters['transform-origin'] =\n  propertyValueConverters.transformOrigin\npropertyValueConverters['-webkit-transform-origin'] =\n  propertyValueConverters.transformOrigin\npropertyValueConverters['-moz-transform-origin'] =\n  propertyValueConverters.transformOrigin\npropertyValueConverters['-webkit-transition'] =\n  propertyValueConverters.transition\npropertyValueConverters['-moz-transition'] = propertyValueConverters.transition\npropertyValueConverters['transition-property'] =\n  propertyValueConverters.transitionProperty\npropertyValueConverters['-webkit-transition-property'] =\n  propertyValueConverters.transitionProperty\npropertyValueConverters['-moz-transition-property'] =\n  propertyValueConverters.transitionProperty\n\nexport default propertyValueConverters\n", "import {\n  includes,\n  arrayToObject,\n  isFunction,\n  isNumber,\n  isObject,\n  isString,\n  canConvertValue,\n} from './utils'\nimport propertyValueConverters from './property-value-converters'\n\n// this will be an object of properties that map to their corresponding rtl property (their doppelganger)\nexport const propertiesToConvert = arrayToObject([\n  ['paddingLeft', 'paddingRight'],\n  ['marginLeft', 'marginRight'],\n  ['left', 'right'],\n  ['borderLeft', 'borderRight'],\n  ['borderLeftColor', 'borderRightColor'],\n  ['borderLeftStyle', 'borderRightStyle'],\n  ['borderLeftWidth', 'borderRightWidth'],\n  ['borderTopLeftRadius', 'borderTopRightRadius'],\n  ['borderBottomLeftRadius', 'borderBottomRightRadius'],\n  // kebab-case versions\n  ['padding-left', 'padding-right'],\n  ['margin-left', 'margin-right'],\n  ['border-left', 'border-right'],\n  ['border-left-color', 'border-right-color'],\n  ['border-left-style', 'border-right-style'],\n  ['border-left-width', 'border-right-width'],\n  ['border-top-left-radius', 'border-top-right-radius'],\n  ['border-bottom-left-radius', 'border-bottom-right-radius'],\n])\n\nexport const propsToIgnore = ['content']\n\n// this is the same as the propertiesToConvert except for values\nexport const valuesToConvert = arrayToObject([\n  ['ltr', 'rtl'],\n  ['left', 'right'],\n  ['w-resize', 'e-resize'],\n  ['sw-resize', 'se-resize'],\n  ['nw-resize', 'ne-resize'],\n])\n\n// Sorry for the regex 😞, but basically thisis used to replace _every_ instance of\n// `ltr`, `rtl`, `right`, and `left` in `backgroundimage` with the corresponding opposite.\n// A situation we're accepting here:\n// url('/left/right/rtl/ltr.png') will be changed to url('/right/left/ltr/rtl.png')\n// Definite trade-offs here, but I think it's a good call.\nconst bgImgDirectionRegex = new RegExp(\n  '(^|\\\\W|_)((ltr)|(rtl)|(left)|(right))(\\\\W|_|$)',\n  'g',\n)\nconst bgPosDirectionRegex = new RegExp('(left)|(right)')\n\n/**\n * converts properties and values in the CSS in JS object to their corresponding RTL values\n * @param {Object} object the CSS in JS object\n * @return {Object} the RTL converted object\n */\nexport function convert(object) {\n  return Object.keys(object).reduce(\n    (newObj, originalKey) => {\n      let originalValue = object[originalKey]\n      if (isString(originalValue)) {\n        // you're welcome to later code 😺\n        originalValue = originalValue.trim()\n      }\n\n      // Some properties should never be transformed\n      if (includes(propsToIgnore, originalKey)) {\n        newObj[originalKey] = originalValue\n        return newObj\n      }\n\n      const {key, value} = convertProperty(originalKey, originalValue)\n      newObj[key] = value\n      return newObj\n    },\n    Array.isArray(object) ? [] : {},\n  )\n}\n\n/**\n * Converts a property and its value to the corresponding RTL key and value\n * @param {String} originalKey the original property key\n * @param {Number|String|Object} originalValue the original css property value\n * @return {Object} the new {key, value} pair\n */\nexport function convertProperty(originalKey, originalValue) {\n  const isNoFlip = /\\/\\*\\s?@noflip\\s?\\*\\//.test(originalValue)\n  const key = isNoFlip ? originalKey : getPropertyDoppelganger(originalKey)\n  const value = isNoFlip\n    ? originalValue\n    : getValueDoppelganger(key, originalValue)\n  return {key, value}\n}\n\n/**\n * This gets the RTL version of the given property if it has a corresponding RTL property\n * @param {String} property the name of the property\n * @return {String} the name of the RTL property\n */\nexport function getPropertyDoppelganger(property) {\n  return propertiesToConvert[property] || property\n}\n\n/**\n * This converts the given value to the RTL version of that value based on the key\n * @param {String} key this is the key (note: this should be the RTL version of the originalKey)\n * @param {String|Number|Object} originalValue the original css property value. If it's an object, then we'll convert that as well\n * @return {String|Number|Object} the converted value\n */\nexport function getValueDoppelganger(key, originalValue) {\n  if (!canConvertValue(originalValue)) {\n    return originalValue\n  }\n\n  if (isObject(originalValue)) {\n    return convert(originalValue) // recursion 🌀\n  }\n  const isNum = isNumber(originalValue)\n  const isFunc = isFunction(originalValue)\n\n  const importantlessValue =\n    isNum || isFunc\n      ? originalValue\n      : originalValue.replace(/ !important.*?$/, '')\n  const isImportant =\n    !isNum && importantlessValue.length !== originalValue.length\n  const valueConverter = propertyValueConverters[key]\n  let newValue\n  if (valueConverter) {\n    newValue = valueConverter({\n      value: importantlessValue,\n      valuesToConvert,\n      propertiesToConvert,\n      isRtl: true,\n      bgImgDirectionRegex,\n      bgPosDirectionRegex,\n    })\n  } else {\n    newValue = valuesToConvert[importantlessValue] || importantlessValue\n  }\n  if (isImportant) {\n    return `${newValue} !important`\n  }\n  return newValue\n}\n"], "names": ["arrayToObject", "array", "reduce", "obj", "prop1", "prop2", "isBoolean", "val", "isFunction", "isNumber", "isNullOrUndefined", "isObject", "isString", "includes", "inclusive", "inclusee", "indexOf", "flipSign", "value", "parseFloat", "slice", "flipTransformSign", "match", "prefix", "offset", "suffix", "calculateNewBackgroundPosition", "idx", "len", "length", "toFixed", "getValuesAsList", "replace", "split", "map", "i", "trim", "filter", "Boolean", "item", "list", "state", "openParansCount", "closedParansCount", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "handleQuartetValues", "splitValues", "top", "right", "bottom", "left", "join", "canConvertValue", "splitShadow", "shadows", "start", "end", "rgba", "substring", "propertyValueConverters", "padding", "textShadow", "flippedShadows", "shadow", "whiteSpace", "negative", "number", "doubleNegative", "borderColor", "borderRadius", "radius1", "radius2", "convertedRadius1", "convertedRadius2", "reverse", "topLeft", "topRight", "bottomRight", "bottomLeft", "background", "valuesToConvert", "isRtl", "bgImgDirectionRegex", "bgPosDirectionRegex", "backgroundPositionValue", "backgroundPosition", "backgroundImage", "g1", "group2", "group", "backgroundPositionX", "transition", "propertiesToConvert", "values", "transitionProperty", "prop", "transform", "nonAsciiPattern", "escapePattern", "signedQuantPattern", "translateXRegExp", "RegExp", "translateRegExp", "translate3dRegExp", "rotateRegExp", "objectPosition", "margin", "borderWidth", "boxShadow", "webkitBoxShadow", "mozBoxShadow", "WebkitBoxShadow", "MozBoxShadow", "borderStyle", "webkitTransform", "mozTransform", "WebkitTransform", "MozTransform", "transform<PERSON><PERSON>in", "webkitTransformOrigin", "mozTransformOrigin", "WebkitTransformOrigin", "MozTransformOrigin", "webkitTransition", "mozTransition", "WebkitTransition", "MozTransition", "webkitTransitionProperty", "mozTransitionProperty", "WebkitTransitionProperty", "MozTransitionProperty", "propsToIgnore", "convert", "object", "Object", "keys", "newObj", "original<PERSON>ey", "originalValue", "convertProperty", "key", "Array", "isArray", "isNoFlip", "test", "getPropertyDoppelganger", "getValueDoppelganger", "property", "isNum", "isFunc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isImportant", "valueConverter", "newValue"], "mappings": ";;;;;;EAAA;EACA;EACA;EACA;EACA;EACA,SAASA,aAAa,CAACC,KAAK,EAAE;IAC5B,OAAOA,KAAK,CAACC,MAAM,CAAC,UAACC,GAAG,QAAqB;MAAA,IAAlBC,KAAK;QAAEC,KAAK;MACrCF,GAAG,CAACC,KAAK,CAAC,GAAGC,KAAK;MAClBF,GAAG,CAACE,KAAK,CAAC,GAAGD,KAAK;MAClB,OAAOD,GAAG;KACX,EAAE,EAAE,CAAC;EACR;EAEA,SAASG,SAAS,CAACC,GAAG,EAAE;IACtB,OAAO,OAAOA,GAAG,KAAK,SAAS;EACjC;EAEA,SAASC,UAAU,CAACD,GAAG,EAAE;IACvB,OAAO,OAAOA,GAAG,KAAK,UAAU;EAClC;EAEA,SAASE,QAAQ,CAACF,GAAG,EAAE;IACrB,OAAO,OAAOA,GAAG,KAAK,QAAQ;EAChC;EAEA,SAASG,iBAAiB,CAACH,GAAG,EAAE;IAC9B,OAAOA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,WAAW;EACnD;EAEA,SAASI,QAAQ,CAACJ,GAAG,EAAE;IACrB,OAAOA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ;EACvC;EAEA,SAASK,QAAQ,CAACL,GAAG,EAAE;IACrB,OAAO,OAAOA,GAAG,KAAK,QAAQ;EAChC;EAEA,SAASM,QAAQ,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACrC,OAAOD,SAAS,CAACE,OAAO,CAACD,QAAQ,CAAC,KAAK,CAAC,CAAC;EAC3C;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASE,QAAQ,CAACC,KAAK,EAAE;IACvB,IAAIC,UAAU,CAACD,KAAK,CAAC,KAAK,CAAC,EAAE;;MAE3B,OAAOA,KAAK;;IAGd,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACpB,OAAOA,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;;IAGvB,aAAWF,KAAK;EAClB;EAEA,SAASG,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;IACxD,OAAOF,MAAM,GAAGN,QAAQ,CAACO,MAAM,CAAC,GAAGC,MAAM;EAC3C;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASC,8BAA8B,CAACR,KAAK,EAAE;IAC7C,IAAMS,GAAG,GAAGT,KAAK,CAACF,OAAO,CAAC,GAAG,CAAC;IAC9B,IAAIW,GAAG,KAAK,CAAC,CAAC,EAAE;MACdT,KAAK,GAAM,GAAG,GAAGC,UAAU,CAACD,KAAK,CAAC,MAAG;KACtC,MAAM;;MAEL,IAAMU,GAAG,GAAGV,KAAK,CAACW,MAAM,GAAGF,GAAG,GAAG,CAAC;MAClCT,KAAK,GAAG,GAAG,GAAGC,UAAU,CAACD,KAAK,CAAC;MAC/BA,KAAK,GAAMA,KAAK,CAACY,OAAO,CAACF,GAAG,CAAC,MAAG;;IAElC,OAAOV,KAAK;EACd;;EAEA;EACA;EACA;EACA;EACA;EACA,SAASa,eAAe,CAACb,KAAK,EAAE;IAC9B,OACEA,KAAK,CACFc,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;KACnBC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACC,IAAI,EAAE;MAAC;KAClBC,MAAM,CAACC,OAAO,CAAC;;;KAGfpC,MAAM,CACL,iBAAgBqC,IAAI,EAAK;MAAA,IAAvBC,IAAI,SAAJA,IAAI;QAAEC,KAAK,SAALA,KAAK;MACX,IAAMC,eAAe,GAAG,CAACH,IAAI,CAACjB,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAEO,MAAM;MACxD,IAAMc,iBAAiB,GAAG,CAACJ,IAAI,CAACjB,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAEO,MAAM;MAC1D,IAAIY,KAAK,CAACG,WAAW,GAAG,CAAC,EAAE;QACzBJ,IAAI,CAACA,IAAI,CAACX,MAAM,GAAG,CAAC,CAAC,GAAMW,IAAI,CAACA,IAAI,CAACX,MAAM,GAAG,CAAC,CAAC,SAAIU,IAAM;OAC3D,MAAM;QACLC,IAAI,CAACK,IAAI,CAACN,IAAI,CAAC;;MAEjBE,KAAK,CAACG,WAAW,IAAIF,eAAe,GAAGC,iBAAiB;MACxD,OAAO;QAACH,IAAI,EAAJA,IAAI;QAAEC,KAAK,EAALA;OAAM;KACrB,EACD;MAACD,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE;QAACG,WAAW,EAAE;;KAAG,CACpC,CAACJ,IAAI;EAEZ;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA,SAASM,mBAAmB,CAAC5B,KAAK,EAAE;IAClC,IAAM6B,WAAW,GAAGhB,eAAe,CAACb,KAAK,CAAC;IAC1C,IAAI6B,WAAW,CAAClB,MAAM,IAAI,CAAC,IAAIkB,WAAW,CAAClB,MAAM,GAAG,CAAC,EAAE;MACrD,OAAOX,KAAK;;IAEd,IAAO8B,GAAG,GAAyBD,WAAW;MAAlCE,KAAK,GAAkBF,WAAW;MAA3BG,MAAM,GAAUH,WAAW;MAAnBI,IAAI,GAAIJ,WAAW;IAC9C,OAAO,CAACC,GAAG,EAAEG,IAAI,EAAED,MAAM,EAAED,KAAK,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;EAC7C;;EAEA;EACA;EACA;EACA;EACA;EACA,SAASC,eAAe,CAACnC,KAAK,EAAE;IAC9B,OAAO,CAACZ,SAAS,CAACY,KAAK,CAAC,IAAI,CAACR,iBAAiB,CAACQ,KAAK,CAAC;EACvD;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA,SAASoC,WAAW,CAACpC,KAAK,EAAE;IAC1B,IAAMqC,OAAO,GAAG,EAAE;IAClB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,IAAI,GAAG,KAAK;IAChB,OAAOD,GAAG,GAAGvC,KAAK,CAACW,MAAM,EAAE;MACzB,IAAI,CAAC6B,IAAI,IAAIxC,KAAK,CAACuC,GAAG,CAAC,KAAK,GAAG,EAAE;QAC/BF,OAAO,CAACV,IAAI,CAAC3B,KAAK,CAACyC,SAAS,CAACH,KAAK,EAAEC,GAAG,CAAC,CAACrB,IAAI,EAAE,CAAC;QAChDqB,GAAG,EAAE;QACLD,KAAK,GAAGC,GAAG;OACZ,MAAM,IAAIvC,KAAK,CAACuC,GAAG,CAAC,QAAQ,EAAE;QAC7BC,IAAI,GAAG,IAAI;QACXD,GAAG,EAAE;OACN,MAAM,IAAIvC,KAAK,CAACuC,GAAG,CAAC,KAAK,GAAG,EAAE;QAC7BC,IAAI,GAAG,KAAK;QACZD,GAAG,EAAE;OACN,MAAM;QACLA,GAAG,EAAE;;;;;;IAMT,IAAID,KAAK,IAAIC,GAAG,EAAE;MAChBF,OAAO,CAACV,IAAI,CAAC3B,KAAK,CAACyC,SAAS,CAACH,KAAK,EAAEC,GAAG,GAAG,CAAC,CAAC,CAAC;;IAG/C,OAAOF,OAAO;EAChB;;ECtKA;EACA,IAAMK,uBAAuB,GAAG;IAC9BC,OAAO,yBAAU;MAAA,IAAR3C,KAAK,QAALA,KAAK;MACZ,IAAIT,QAAQ,CAACS,KAAK,CAAC,EAAE;QACnB,OAAOA,KAAK;;MAEd,OAAO4B,mBAAmB,CAAC5B,KAAK,CAAC;KAClC;IACD4C,UAAU,6BAAU;MAAA,IAAR5C,KAAK,SAALA,KAAK;MACf,IAAM6C,cAAc,GAAGT,WAAW,CAACpC,KAAK,CAAC,CAACgB,GAAG,CAAC,UAAA8B,MAAM,EAAI;;QAEtD,OAAOA,MAAM,CAAChC,OAAO,CACnB,qBAAqB,EACrB,UAACV,KAAK,EAAE2C,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAK;UACvC,IAAIA,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO7C,KAAK;;UAEd,IAAM8C,cAAc,GAAGF,QAAQ,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE;UACjD,YAAUD,UAAU,GAAGG,cAAc,GAAGD,MAAM;SAC/C,CACF;OACF,CAAC;MAEF,OAAOJ,cAAc,CAACX,IAAI,CAAC,GAAG,CAAC;KAChC;IACDiB,WAAW,8BAAU;MAAA,IAARnD,KAAK,SAALA,KAAK;MAChB,OAAO4B,mBAAmB,CAAC5B,KAAK,CAAC;KAClC;IACDoD,YAAY,+BAAU;MAAA,IAARpD,KAAK,SAALA,KAAK;MACjB,IAAIT,QAAQ,CAACS,KAAK,CAAC,EAAE;QACnB,OAAOA,KAAK;;MAEd,IAAIL,QAAQ,CAACK,KAAK,EAAE,GAAG,CAAC,EAAE;QACxB,mBAA2BA,KAAK,CAACe,KAAK,CAAC,GAAG,CAAC;UAApCsC,OAAO;UAAEC,OAAO;QACvB,IAAMC,gBAAgB,GAAGb,uBAAuB,CAACU,YAAY,CAAC;UAC5DpD,KAAK,EAAEqD,OAAO,CAACnC,IAAI;SACpB,CAAC;QACF,IAAMsC,gBAAgB,GAAGd,uBAAuB,CAACU,YAAY,CAAC;UAC5DpD,KAAK,EAAEsD,OAAO,CAACpC,IAAI;SACpB,CAAC;QACF,OAAUqC,gBAAgB,WAAMC,gBAAgB;;MAElD,IAAM3B,WAAW,GAAGhB,eAAe,CAACb,KAAK,CAAC;MAC1C,QAAQ6B,WAAW,CAAClB,MAAM;QACxB,KAAK,CAAC;UAAE;YACN,OAAOkB,WAAW,CAAC4B,OAAO,EAAE,CAACvB,IAAI,CAAC,GAAG,CAAC;;QAExC,KAAK,CAAC;UAAE;YACN,IAAOwB,OAAO,GAAuC7B,WAAW;cAAhD8B,QAAQ,GAA6B9B,WAAW;cAAtC+B,WAAW,GAAgB/B,WAAW;cAAzBgC,UAAU,GAAIhC,WAAW;YAChE,OAAO,CAAC8B,QAAQ,EAAED,OAAO,EAAEG,UAAU,EAAED,WAAW,CAAC,CAAC1B,IAAI,CAAC,GAAG,CAAC;;QAE/D;UAAS;YACP,OAAOlC,KAAK;;;KAGjB;IACD8D,UAAU,6BAMP;MAAA,IALD9D,KAAK,SAALA,KAAK;QACL+D,eAAe,SAAfA,eAAe;QACfC,KAAK,SAALA,KAAK;QACLC,mBAAmB,SAAnBA,mBAAmB;QACnBC,mBAAmB,SAAnBA,mBAAmB;MAEnB,IAAI3E,QAAQ,CAACS,KAAK,CAAC,EAAE;QACnB,OAAOA,KAAK;;;;;;MAMd,IAAMmE,uBAAuB,GAAGnE,KAAK,CAClCc,OAAO,CACN,6EAA6E,EAC7E,EAAE,CACH,CACAI,IAAI,EAAE;;MAETlB,KAAK,GAAGA,KAAK,CAACc,OAAO,CACnBqD,uBAAuB,EACvBzB,uBAAuB,CAAC0B,kBAAkB,CAAC;QACzCpE,KAAK,EAAEmE,uBAAuB;QAC9BJ,eAAe,EAAfA,eAAe;QACfC,KAAK,EAALA,KAAK;QACLE,mBAAmB,EAAnBA;OACD,CAAC,CACH;;MAED,OAAOxB,uBAAuB,CAAC2B,eAAe,CAAC;QAC7CrE,KAAK,EAALA,KAAK;QACL+D,eAAe,EAAfA,eAAe;QACfE,mBAAmB,EAAnBA;OACD,CAAC;KACH;IACDI,eAAe,kCAAgD;MAAA,IAA9CrE,KAAK,SAALA,KAAK;QAAE+D,eAAe,SAAfA,eAAe;QAAEE,mBAAmB,SAAnBA,mBAAmB;MAC1D,IAAI,CAACtE,QAAQ,CAACK,KAAK,EAAE,MAAM,CAAC,IAAI,CAACL,QAAQ,CAACK,KAAK,EAAE,kBAAkB,CAAC,EAAE;QACpE,OAAOA,KAAK;;MAEd,OAAOA,KAAK,CAACc,OAAO,CAACmD,mBAAmB,EAAE,UAAC7D,KAAK,EAAEkE,EAAE,EAAEC,MAAM,EAAK;QAC/D,OAAOnE,KAAK,CAACU,OAAO,CAACyD,MAAM,EAAER,eAAe,CAACQ,MAAM,CAAC,CAAC;OACtD,CAAC;KACH;IACDH,kBAAkB,qCAAuD;MAAA,IAArDpE,KAAK,SAALA,KAAK;QAAE+D,eAAe,SAAfA,eAAe;QAAEC,KAAK,SAALA,KAAK;QAAEE,mBAAmB,SAAnBA,mBAAmB;MACpE,OACElE;;OAEGc,OAAO,CAACkD,KAAK,GAAG,gBAAgB,GAAG,IAAI,EAAE,UAAC5D,KAAK,EAAEoE,KAAK;QAAA,OACrDhE,8BAA8B,CAACgE,KAAK,CAAC;QACtC,CACA1D,OAAO,CAACoD,mBAAmB,EAAE,UAAA9D,KAAK;QAAA,OAAI2D,eAAe,CAAC3D,KAAK,CAAC;QAAC;KAEnE;IACDqE,mBAAmB,sCAAuD;MAAA,IAArDzE,KAAK,SAALA,KAAK;QAAE+D,eAAe,SAAfA,eAAe;QAAEC,KAAK,SAALA,KAAK;QAAEE,mBAAmB,SAAnBA,mBAAmB;MACrE,IAAI3E,QAAQ,CAACS,KAAK,CAAC,EAAE;QACnB,OAAOA,KAAK;;MAEd,OAAO0C,uBAAuB,CAAC0B,kBAAkB,CAAC;QAChDpE,KAAK,EAALA,KAAK;QACL+D,eAAe,EAAfA,eAAe;QACfC,KAAK,EAALA,KAAK;QACLE,mBAAmB,EAAnBA;OACD,CAAC;KACH;IACDQ,UAAU,6BAA+B;MAAA,IAA7B1E,KAAK,SAALA,KAAK;QAAE2E,mBAAmB,SAAnBA,mBAAmB;MACpC,OAAO3E,KAAK,CACTe,KAAK,CAAC,OAAO,CAAC,CACdC,GAAG,CAAC,UAAA0D,UAAU,EAAI;QACjB,IAAME,MAAM,GAAGF,UAAU,CAAC3D,KAAK,CAAC,GAAG,CAAC;;;QAGpC6D,MAAM,CAAC,CAAC,CAAC,GAAGD,mBAAmB,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC;QAEvD,OAAOA,MAAM,CAAC1C,IAAI,CAAC,GAAG,CAAC;OACxB,CAAC,CACDA,IAAI,CAAC,IAAI,CAAC;KACd;IACD2C,kBAAkB,sCAA+B;MAAA,IAA7B7E,KAAK,UAALA,KAAK;QAAE2E,mBAAmB,UAAnBA,mBAAmB;MAC5C,OAAO3E,KAAK,CACTe,KAAK,CAAC,OAAO,CAAC,CACdC,GAAG,CAAC,UAAA8D,IAAI;QAAA,OAAIH,mBAAmB,CAACG,IAAI,CAAC,IAAIA,IAAI;QAAC,CAC9C5C,IAAI,CAAC,IAAI,CAAC;KACd;IACD6C,SAAS,6BAAU;MAAA,IAAR/E,KAAK,UAALA,KAAK;;;MAGd,IAAMgF,eAAe,GAAG,oBAAoB;MAI5C,IAAMC,aAAa,WAHI,wCAAwC,6BAGK;MAKpE,IAAMC,kBAAkB,eAPL,4BAA4B,eAC3B,wDAAwD,iCAExCF,eAAe,SAAIC,aAAa,8BAC7BD,eAAe,SAAIC,aAAa,8CAGF;MACrE,IAAME,gBAAgB,GAAG,IAAIC,MAAM,6BACPF,kBAAkB,gBAC5C,IAAI,CACL;MACD,IAAMG,eAAe,GAAG,IAAID,MAAM,4BACPF,kBAAkB,qBAAgBA,kBAAkB,qBAC7E,IAAI,CACL;MACD,IAAMI,iBAAiB,GAAG,IAAIF,MAAM,8BACPF,kBAAkB,qBAAgBA,kBAAkB,qBAC/E,IAAI,CACL;MACD,IAAMK,YAAY,GAAG,IAAIH,MAAM,8BACFF,kBAAkB,gBAC7C,IAAI,CACL;MACD,OAAOlF,KAAK,CACTc,OAAO,CAACqE,gBAAgB,EAAEhF,iBAAiB,CAAC,CAC5CW,OAAO,CAACuE,eAAe,EAAElF,iBAAiB,CAAC,CAC3CW,OAAO,CAACwE,iBAAiB,EAAEnF,iBAAiB,CAAC,CAC7CW,OAAO,CAACyE,YAAY,EAAEpF,iBAAiB,CAAC;;EAE/C,CAAC;EAEDuC,uBAAuB,CAAC8C,cAAc,GACpC9C,uBAAuB,CAAC0B,kBAAkB;EAC5C1B,uBAAuB,CAAC+C,MAAM,GAAG/C,uBAAuB,CAACC,OAAO;EAChED,uBAAuB,CAACgD,WAAW,GAAGhD,uBAAuB,CAACC,OAAO;EACrED,uBAAuB,CAACiD,SAAS,GAAGjD,uBAAuB,CAACE,UAAU;EACtEF,uBAAuB,CAACkD,eAAe,GAAGlD,uBAAuB,CAACiD,SAAS;EAC3EjD,uBAAuB,CAACmD,YAAY,GAAGnD,uBAAuB,CAACiD,SAAS;EACxEjD,uBAAuB,CAACoD,eAAe,GAAGpD,uBAAuB,CAACiD,SAAS;EAC3EjD,uBAAuB,CAACqD,YAAY,GAAGrD,uBAAuB,CAACiD,SAAS;EACxEjD,uBAAuB,CAACsD,WAAW,GAAGtD,uBAAuB,CAACS,WAAW;EACzET,uBAAuB,CAACuD,eAAe,GAAGvD,uBAAuB,CAACqC,SAAS;EAC3ErC,uBAAuB,CAACwD,YAAY,GAAGxD,uBAAuB,CAACqC,SAAS;EACxErC,uBAAuB,CAACyD,eAAe,GAAGzD,uBAAuB,CAACqC,SAAS;EAC3ErC,uBAAuB,CAAC0D,YAAY,GAAG1D,uBAAuB,CAACqC,SAAS;EACxErC,uBAAuB,CAAC2D,eAAe,GACrC3D,uBAAuB,CAAC0B,kBAAkB;EAC5C1B,uBAAuB,CAAC4D,qBAAqB,GAC3C5D,uBAAuB,CAAC2D,eAAe;EACzC3D,uBAAuB,CAAC6D,kBAAkB,GACxC7D,uBAAuB,CAAC2D,eAAe;EACzC3D,uBAAuB,CAAC8D,qBAAqB,GAC3C9D,uBAAuB,CAAC2D,eAAe;EACzC3D,uBAAuB,CAAC+D,kBAAkB,GACxC/D,uBAAuB,CAAC2D,eAAe;EACzC3D,uBAAuB,CAACgE,gBAAgB,GAAGhE,uBAAuB,CAACgC,UAAU;EAC7EhC,uBAAuB,CAACiE,aAAa,GAAGjE,uBAAuB,CAACgC,UAAU;EAC1EhC,uBAAuB,CAACkE,gBAAgB,GAAGlE,uBAAuB,CAACgC,UAAU;EAC7EhC,uBAAuB,CAACmE,aAAa,GAAGnE,uBAAuB,CAACgC,UAAU;EAC1EhC,uBAAuB,CAACoE,wBAAwB,GAC9CpE,uBAAuB,CAACmC,kBAAkB;EAC5CnC,uBAAuB,CAACqE,qBAAqB,GAC3CrE,uBAAuB,CAACmC,kBAAkB;EAC5CnC,uBAAuB,CAACsE,wBAAwB,GAC9CtE,uBAAuB,CAACmC,kBAAkB;EAC5CnC,uBAAuB,CAACuE,qBAAqB,GAC3CvE,uBAAuB,CAACmC,kBAAkB;;EAE5C;;EAEAnC,uBAAuB,CAAC,aAAa,CAAC,GAAGA,uBAAuB,CAACE,UAAU;EAC3EF,uBAAuB,CAAC,cAAc,CAAC,GAAGA,uBAAuB,CAACS,WAAW;EAC7ET,uBAAuB,CAAC,eAAe,CAAC,GAAGA,uBAAuB,CAACU,YAAY;EAC/EV,uBAAuB,CAAC,kBAAkB,CAAC,GACzCA,uBAAuB,CAAC2B,eAAe;EACzC3B,uBAAuB,CAAC,qBAAqB,CAAC,GAC5CA,uBAAuB,CAAC0B,kBAAkB;EAC5C1B,uBAAuB,CAAC,uBAAuB,CAAC,GAC9CA,uBAAuB,CAAC+B,mBAAmB;EAC7C/B,uBAAuB,CAAC,iBAAiB,CAAC,GACxCA,uBAAuB,CAAC8C,cAAc;EACxC9C,uBAAuB,CAAC,cAAc,CAAC,GAAGA,uBAAuB,CAACC,OAAO;EACzED,uBAAuB,CAAC,YAAY,CAAC,GAAGA,uBAAuB,CAACE,UAAU;EAC1EF,uBAAuB,CAAC,oBAAoB,CAAC,GAC3CA,uBAAuB,CAACE,UAAU;EACpCF,uBAAuB,CAAC,iBAAiB,CAAC,GAAGA,uBAAuB,CAACE,UAAU;EAC/EF,uBAAuB,CAAC,cAAc,CAAC,GAAGA,uBAAuB,CAACS,WAAW;EAC7ET,uBAAuB,CAAC,mBAAmB,CAAC,GAAGA,uBAAuB,CAACqC,SAAS;EAChFrC,uBAAuB,CAAC,gBAAgB,CAAC,GAAGA,uBAAuB,CAACqC,SAAS;EAC7ErC,uBAAuB,CAAC,kBAAkB,CAAC,GACzCA,uBAAuB,CAAC2D,eAAe;EACzC3D,uBAAuB,CAAC,0BAA0B,CAAC,GACjDA,uBAAuB,CAAC2D,eAAe;EACzC3D,uBAAuB,CAAC,uBAAuB,CAAC,GAC9CA,uBAAuB,CAAC2D,eAAe;EACzC3D,uBAAuB,CAAC,oBAAoB,CAAC,GAC3CA,uBAAuB,CAACgC,UAAU;EACpChC,uBAAuB,CAAC,iBAAiB,CAAC,GAAGA,uBAAuB,CAACgC,UAAU;EAC/EhC,uBAAuB,CAAC,qBAAqB,CAAC,GAC5CA,uBAAuB,CAACmC,kBAAkB;EAC5CnC,uBAAuB,CAAC,6BAA6B,CAAC,GACpDA,uBAAuB,CAACmC,kBAAkB;EAC5CnC,uBAAuB,CAAC,0BAA0B,CAAC,GACjDA,uBAAuB,CAACmC,kBAAkB;;ECzP5C;AACA,EAAO,IAAMF,mBAAmB,GAAG7F,aAAa,CAAC,CAC/C,CAAC,aAAa,EAAE,cAAc,CAAC,EAC/B,CAAC,YAAY,EAAE,aAAa,CAAC,EAC7B,CAAC,MAAM,EAAE,OAAO,CAAC,EACjB,CAAC,YAAY,EAAE,aAAa,CAAC,EAC7B,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,EACvC,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,EACvC,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,EACvC,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,EAC/C,CAAC,wBAAwB,EAAE,yBAAyB,CAAC;EACrD;EACA,CAAC,cAAc,EAAE,eAAe,CAAC,EACjC,CAAC,aAAa,EAAE,cAAc,CAAC,EAC/B,CAAC,aAAa,EAAE,cAAc,CAAC,EAC/B,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EAC3C,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EAC3C,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EAC3C,CAAC,wBAAwB,EAAE,yBAAyB,CAAC,EACrD,CAAC,2BAA2B,EAAE,4BAA4B,CAAC,CAC5D,CAAC;AAEF,EAAO,IAAMoI,aAAa,GAAG,CAAC,SAAS,CAAC;;EAExC;AACA,EAAO,IAAMnD,eAAe,GAAGjF,aAAa,CAAC,CAC3C,CAAC,KAAK,EAAE,KAAK,CAAC,EACd,CAAC,MAAM,EAAE,OAAO,CAAC,EACjB,CAAC,UAAU,EAAE,UAAU,CAAC,EACxB,CAAC,WAAW,EAAE,WAAW,CAAC,EAC1B,CAAC,WAAW,EAAE,WAAW,CAAC,CAC3B,CAAC;;EAEF;EACA;EACA;EACA;EACA;EACA,IAAMmF,mBAAmB,GAAG,IAAImB,MAAM,CACpC,gDAAgD,EAChD,GAAG,CACJ;EACD,IAAMlB,mBAAmB,GAAG,IAAIkB,MAAM,CAAC,gBAAgB,CAAC;;EAExD;EACA;EACA;EACA;EACA;AACA,EAAO,SAAS+B,OAAO,CAACC,MAAM,EAAE;IAC9B,OAAOC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACpI,MAAM,CAC/B,UAACuI,MAAM,EAAEC,WAAW,EAAK;MACvB,IAAIC,aAAa,GAAGL,MAAM,CAACI,WAAW,CAAC;MACvC,IAAI9H,QAAQ,CAAC+H,aAAa,CAAC,EAAE;;QAE3BA,aAAa,GAAGA,aAAa,CAACvG,IAAI,EAAE;;;;MAItC,IAAIvB,QAAQ,CAACuH,aAAa,EAAEM,WAAW,CAAC,EAAE;QACxCD,MAAM,CAACC,WAAW,CAAC,GAAGC,aAAa;QACnC,OAAOF,MAAM;;MAGf,uBAAqBG,eAAe,CAACF,WAAW,EAAEC,aAAa,CAAC;QAAzDE,GAAG,oBAAHA,GAAG;QAAE3H,KAAK,oBAALA,KAAK;MACjBuH,MAAM,CAACI,GAAG,CAAC,GAAG3H,KAAK;MACnB,OAAOuH,MAAM;KACd,EACDK,KAAK,CAACC,OAAO,CAACT,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAChC;EACH;;EAEA;EACA;EACA;EACA;EACA;EACA;AACA,EAAO,SAASM,eAAe,CAACF,WAAW,EAAEC,aAAa,EAAE;IAC1D,IAAMK,QAAQ,GAAG,uBAAuB,CAACC,IAAI,CAACN,aAAa,CAAC;IAC5D,IAAME,GAAG,GAAGG,QAAQ,GAAGN,WAAW,GAAGQ,uBAAuB,CAACR,WAAW,CAAC;IACzE,IAAMxH,KAAK,GAAG8H,QAAQ,GAClBL,aAAa,GACbQ,oBAAoB,CAACN,GAAG,EAAEF,aAAa,CAAC;IAC5C,OAAO;MAACE,GAAG,EAAHA,GAAG;MAAE3H,KAAK,EAALA;KAAM;EACrB;;EAEA;EACA;EACA;EACA;EACA;AACA,EAAO,SAASgI,uBAAuB,CAACE,QAAQ,EAAE;IAChD,OAAOvD,mBAAmB,CAACuD,QAAQ,CAAC,IAAIA,QAAQ;EAClD;;EAEA;EACA;EACA;EACA;EACA;EACA;AACA,EAAO,SAASD,oBAAoB,CAACN,GAAG,EAAEF,aAAa,EAAE;IACvD,IAAI,CAACtF,eAAe,CAACsF,aAAa,CAAC,EAAE;MACnC,OAAOA,aAAa;;IAGtB,IAAIhI,QAAQ,CAACgI,aAAa,CAAC,EAAE;MAC3B,OAAON,OAAO,CAACM,aAAa,CAAC;;;IAE/B,IAAMU,KAAK,GAAG5I,QAAQ,CAACkI,aAAa,CAAC;IACrC,IAAMW,MAAM,GAAG9I,UAAU,CAACmI,aAAa,CAAC;IAExC,IAAMY,kBAAkB,GACtBF,KAAK,IAAIC,MAAM,GACXX,aAAa,GACbA,aAAa,CAAC3G,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;IAClD,IAAMwH,WAAW,GACf,CAACH,KAAK,IAAIE,kBAAkB,CAAC1H,MAAM,KAAK8G,aAAa,CAAC9G,MAAM;IAC9D,IAAM4H,cAAc,GAAG7F,uBAAuB,CAACiF,GAAG,CAAC;IACnD,IAAIa,QAAQ;IACZ,IAAID,cAAc,EAAE;MAClBC,QAAQ,GAAGD,cAAc,CAAC;QACxBvI,KAAK,EAAEqI,kBAAkB;QACzBtE,eAAe,EAAfA,eAAe;QACfY,mBAAmB,EAAnBA,mBAAmB;QACnBX,KAAK,EAAE,IAAI;QACXC,mBAAmB,EAAnBA,mBAAmB;QACnBC,mBAAmB,EAAnBA;OACD,CAAC;KACH,MAAM;MACLsE,QAAQ,GAAGzE,eAAe,CAACsE,kBAAkB,CAAC,IAAIA,kBAAkB;;IAEtE,IAAIC,WAAW,EAAE;MACf,OAAUE,QAAQ;;IAEpB,OAAOA,QAAQ;EACjB;;;;;;;;"}